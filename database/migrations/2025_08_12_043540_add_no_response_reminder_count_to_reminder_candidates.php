<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\ReminderCandidate;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Pour MongoDB, nous devons ajouter le champ directement dans le modèle
        // et initialiser les données existantes

        // Initialiser le nouveau champ pour tous les enregistrements existants
        // no_response_reminder_count = nombre de rappels où le statut est resté "no_response"
        ReminderCandidate::where('response_status', 'no_response')
            ->update(['no_response_reminder_count' => function($doc) {
                return $doc['reminder_count'] ?? 0;
            }]);

        // Pour les candidats qui ont répondu, le compteur reste à 0
        ReminderCandidate::whereIn('response_status', ['yes', 'no'])
            ->update(['no_response_reminder_count' => 0]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Supprimer le champ no_response_reminder_count
        ReminderCandidate::unset('no_response_reminder_count');
    }
};
