<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use App\Models\Formation;
use App\Models\Skill;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // \App\Models\User::factory(10)->create();

        // \App\Models\User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);

        $this->call(RoleSeeder::class);
        $this->call(SkillSeeder::class);
        $this->call(LanguageSeeder::class);
        $this->call(CountrySeeder::class);
        $this->call(PermitSeeder::class);
        $this->call(ResidencePermitSeeder::class);
        $this->call(CivilitySeeder::class);
        $this->call(FieldActivitySeeder::class);
        $this->call(TypeProfessionSeeder::class);
        $this->call(ProfessionSeeder::class);
        $this->call(ResponsibilityCandidateSeeder::class);
        $this->call(FormationSeeder::class);
        $this->call(AdminSeeder::class);
        $this->call(RecruterSeeder::class);
        // $this->call(CandidateSeeder::class);
        $this->call(PlanSeeder::class);
        $this->call(ConversationSeeder::class);
        $this->call(ConfigGlobalAppSeeder::class);
        $this->call(PrioritySeeder::class);
        $this->call(SeveritySeeder::class);
        $this->call(RegionSeeder::class);
        $this->call(PackageSeeder::class);
    }
}
