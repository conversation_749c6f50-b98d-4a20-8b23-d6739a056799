<x-admin-layout>
    <x-slot name="title">Gestion des Rappels Candidats</x-slot>

    @push('head')
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @endpush

    <div class="container mx-auto px-4 py-8">
        <!-- En-tête avec statistiques -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Gestion des Rappels Candidats</h1>
            
            <!-- Statistiques -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total candidats</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $stats['total'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Toujours en recherche d'emploi</p>
                            <p class="text-2xl font-bold text-green-600">{{ $stats['yes_count'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-red-100 rounded-lg">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Déjà trouvé</p>
                            <p class="text-2xl font-bold text-red-600">{{ $stats['no_count'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-gray-100 rounded-lg">
                            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Sans réponse</p>
                            <p class="text-2xl font-bold text-gray-600">{{ $stats['no_response_count'] }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtres et tri -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Filtres et tri</h3>
                    @if(request()->hasAny(['response_status', 'search']))
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd"></path>
                                </svg>
                                Filtres actifs
                            </span>
                            <a href="{{ route('admin.reminder-candidates.index') }}"
                               class="text-sm text-blue-600 hover:text-blue-800">
                                Tout effacer
                            </a>
                        </div>
                    @endif
                </div>

                <form method="GET" action="{{ route('admin.reminder-candidates.index') }}" id="filter-form">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <!-- Filtre par statut -->
                        <div>
                            <label for="response_status" class="block text-sm font-medium text-gray-700 mb-2">
                                Statut
                            </label>
                            <select name="response_status" id="response_status"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Tous les statuts</option>
                                <option value="no_response" {{ request('response_status') == 'no_response' ? 'selected' : '' }}>
                                    Pas de réponse
                                </option>
                                <option value="yes" {{ request('response_status') == 'yes' ? 'selected' : '' }}>
                                    Oui
                                </option>
                                <option value="no" {{ request('response_status') == 'no' ? 'selected' : '' }}>
                                    Non
                                </option>
                            </select>
                        </div>

                        <!-- Tri par colonne -->
                        <div>
                            <label for="sort_by" class="block text-sm font-medium text-gray-700 mb-2">
                                Trier par
                            </label>
                            <select name="sort_by" id="sort_by"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="last_reminder" {{ ($sortBy ?? 'last_reminder') == 'last_reminder' ? 'selected' : '' }}>
                                    Dernier rappel
                                </option>
                                <option value="reminder_count" {{ ($sortBy ?? '') == 'reminder_count' ? 'selected' : '' }}>
                                    Nb rappels
                                </option>
                                <option value="next_reminder" {{ ($sortBy ?? '') == 'next_reminder' ? 'selected' : '' }}>
                                    Prochain rappel
                                </option>
                            </select>
                        </div>

                        <!-- Direction du tri -->
                        <div>
                            <label for="sort_direction" class="block text-sm font-medium text-gray-700 mb-2">
                                Ordre
                            </label>
                            <select name="sort_direction" id="sort_direction"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="asc" {{ ($sortDirection ?? 'asc') == 'asc' ? 'selected' : '' }}>
                                    Croissant
                                </option>
                                <option value="desc" {{ ($sortDirection ?? '') == 'desc' ? 'selected' : '' }}>
                                    Décroissant
                                </option>
                            </select>
                        </div>

                        <!-- Recherche -->
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
                                Recherche
                            </label>
                            <input type="text" name="search" id="search"
                                   value="{{ request('search') }}"
                                   placeholder="Nom, prénom ou email..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <div class="flex space-x-3">
                        <button type="submit"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Appliquer les filtres
                        </button>
                        <a href="{{ route('admin.reminder-candidates.index') }}"
                           class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            Réinitialiser
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Actions groupées -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6">
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-4">
                        <button id="select-all-btn"
                                class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                            Tout sélectionner
                        </button>
                        <button id="bulk-email-btn"
                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled>
                            Envoyer email groupé
                        </button>
                        <span id="selected-count" class="text-sm text-gray-600">0 candidat(s) sélectionné(s)</span>
                    </div>

                    <div class="flex space-x-2">
                        <a href="{{ route('admin.reminder-candidates.export', request()->query()) }}"
                           class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Exporter CSV
                        </a>
                        <button id="test-email-btn"
                                class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                            Tester Email
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tableau des rappels -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="select-all-checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Candidat
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Email
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Statut
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="{{ request()->fullUrlWithQuery(['sort_by' => 'last_reminder', 'sort_direction' => (($sortBy ?? 'last_reminder') == 'last_reminder' && ($sortDirection ?? 'asc') == 'asc') ? 'desc' : 'asc']) }}"
                                   class="flex items-center space-x-1 hover:text-gray-700">
                                    <span>Dernier rappel</span>
                                    @if(($sortBy ?? 'last_reminder') == 'last_reminder')
                                        @if(($sortDirection ?? 'asc') == 'asc')
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
                                            </svg>
                                        @else
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"/>
                                            </svg>
                                        @endif
                                    @else
                                        <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
                                        </svg>
                                    @endif
                                </a>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="{{ request()->fullUrlWithQuery(['sort_by' => 'reminder_count', 'sort_direction' => (($sortBy ?? '') == 'reminder_count' && ($sortDirection ?? '') == 'asc') ? 'desc' : 'asc']) }}"
                                   class="flex items-center space-x-1 hover:text-gray-700">
                                    <span>Nb rappels</span>
                                    @if(($sortBy ?? '') == 'reminder_count')
                                        @if(($sortDirection ?? '') == 'asc')
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
                                            </svg>
                                        @else
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"/>
                                            </svg>
                                        @endif
                                    @else
                                        <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
                                        </svg>
                                    @endif
                                </a>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="{{ request()->fullUrlWithQuery(['sort_by' => 'next_reminder', 'sort_direction' => (($sortBy ?? '') == 'next_reminder' && ($sortDirection ?? '') == 'asc') ? 'desc' : 'asc']) }}"
                                   class="flex items-center space-x-1 hover:text-gray-700">
                                    <span>Prochain rappel</span>
                                    @if(($sortBy ?? '') == 'next_reminder')
                                        @if(($sortDirection ?? '') == 'asc')
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
                                            </svg>
                                        @else
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"/>
                                            </svg>
                                        @endif
                                    @else
                                        <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
                                        </svg>
                                    @endif
                                </a>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($reminders as $reminder)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" 
                                           class="reminder-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" 
                                           value="{{ $reminder->id }}">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ $reminder->user->civility->first_name ?? '' }} {{ $reminder->user->civility->last_name ?? '' }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $reminder->user->email }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap" id="status-cell-{{ $reminder->id }}">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $reminder->status_badge_color }}"
                                          id="status-badge-{{ $reminder->id }}">
                                        {{ $reminder->formatted_response_status }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" id="last-reminder-cell-{{ $reminder->id }}">
                                    {{ $reminder->last_reminder_sent_at ? $reminder->last_reminder_sent_at->format('d/m/Y H:i') : '-' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" id="reminder-count-cell-{{ $reminder->id }}">
                                    {{ $reminder->no_response_reminder_count ?? 0 }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $reminder->next_reminder_date ? $reminder->next_reminder_date->format('d/m/Y') : '-' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button class="send-individual-email text-blue-600 hover:text-blue-900"
                                                data-reminder-id="{{ $reminder->id }}"
                                                data-candidate-name="{{ $reminder->user->firstname() }}"
                                                data-candidate-email="{{ $reminder->user->email }}">
                                            Envoyer email
                                        </button>
                                        <select class="update-status text-xs border-gray-300 rounded"
                                                data-reminder-id="{{ $reminder->id }}">
                                            <option value="no_response" {{ $reminder->response_status == 'no_response' ? 'selected' : '' }}>Pas de réponse</option>
                                            <option value="yes" {{ $reminder->response_status == 'yes' ? 'selected' : '' }}>Oui</option>
                                            <option value="no" {{ $reminder->response_status == 'no' ? 'selected' : '' }}>Non</option>
                                        </select>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="px-6 py-4 text-center">
                                    <div class="flex flex-col items-center justify-center py-8">
                                        <svg class="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        @if(request()->hasAny(['response_status', 'search', 'sort_by', 'sort_direction']))
                                            <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun résultat trouvé</h3>
                                            <p class="text-gray-500 mb-4 text-center max-w-md">
                                                Aucun rappel candidat ne correspond aux critères de recherche ou filtres appliqués.
                                            </p>
                                            <div class="flex space-x-3">
                                                <a href="{{ route('admin.reminder-candidates.index') }}"
                                                   class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    Réinitialiser les filtres
                                                </a>
                                                <button onclick="history.back()"
                                                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                                                    Retour
                                                </button>
                                            </div>
                                        @else
                                            <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun rappel candidat</h3>
                                            <p class="text-gray-500 text-center max-w-md">
                                                Il n'y a actuellement aucun rappel candidat dans le système.
                                                Les rappels seront créés automatiquement selon la logique définie.
                                            </p>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Zone de notifications -->
    <div id="notification-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Modal d'envoi d'email individuel -->
    <div id="individual-email-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Envoyer un email de rappel</h3>
                    <button id="close-individual-modal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form id="individual-email-form" method="POST">
                    @csrf
                    <input type="hidden" id="individual-reminder-id" name="reminder_id">

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Destinataire</label>
                        <p id="individual-recipient" class="text-sm text-gray-600 bg-gray-50 p-2 rounded"></p>
                    </div>

                    <div class="mb-4">
                        <label for="individual-subject" class="block text-sm font-medium text-gray-700 mb-2">Sujet</label>
                        <input type="text" id="individual-subject" name="subject"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               required>
                    </div>

                    <div class="mb-6">
                        <label for="individual-message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                        <textarea id="individual-message" name="message" rows="6"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  required></textarea>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" id="cancel-individual-email"
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Annuler
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Envoyer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal d'envoi d'email groupé -->
    <div id="bulk-email-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Envoyer un email groupé</h3>
                    <button id="close-bulk-modal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form id="bulk-email-form" method="POST" action="{{ route('admin.reminder-candidates.send-bulk-emails') }}">
                    @csrf
                    <input type="hidden" id="bulk-reminder-ids" name="reminder_ids">

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Destinataires</label>
                        <p id="bulk-recipients" class="text-sm text-gray-600 bg-gray-50 p-2 rounded"></p>
                    </div>

                    <div class="mb-4">
                        <label for="bulk-subject" class="block text-sm font-medium text-gray-700 mb-2">Sujet</label>
                        <input type="text" id="bulk-subject" name="subject"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               required>
                    </div>

                    <div class="mb-6">
                        <label for="bulk-message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                        <textarea id="bulk-message" name="message" rows="6"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  required></textarea>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" id="cancel-bulk-email"
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Annuler
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                            Envoyer à tous
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal de test d'email -->
    <div id="test-email-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Tester l'envoi d'email</h3>
                    <button id="close-test-modal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form id="test-email-form" method="POST" action="{{ route('admin.reminder-candidates.test-email') }}">
                    @csrf
                    <div class="mb-4">
                        <label for="test-email-address" class="block text-sm font-medium text-gray-700 mb-2">Email de test</label>
                        <input type="email" id="test-email-address" name="email"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="<EMAIL>"
                               required>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" id="cancel-test-email"
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Annuler
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700">
                            Envoyer test
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // VERSION ULTRA-SIMPLIFIÉE POUR TEST
        console.log('🚀 SCRIPT CHARGÉ');
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔥 DOM PRÊT');

            // Test de base
            const checkboxes = document.querySelectorAll('.reminder-checkbox');
            const counter = document.getElementById('selected-count');
            const bulkBtn = document.getElementById('bulk-email-btn');

            console.log('ÉLÉMENTS:', checkboxes.length, !!counter, !!bulkBtn);

            if (checkboxes.length === 0) {
                alert('ERREUR: Aucune checkbox trouvée !');
                return;
            }

            // Fonction pour afficher les notifications
            function showNotification(message, type = 'success', duration = 5000) {
                const container = document.getElementById('notification-container');
                const notification = document.createElement('div');

                const bgColor = type === 'success' ? 'bg-green-500' :
                               type === 'error' ? 'bg-red-500' :
                               type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500';

                const icon = type === 'success' ? '✅' :
                            type === 'error' ? '❌' :
                            type === 'warning' ? '⚠️' : 'ℹ️';

                notification.className = `${bgColor} text-white px-6 py-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full opacity-0`;
                notification.innerHTML = `
                    <div class="flex items-center space-x-3">
                        <span class="text-lg">${icon}</span>
                        <span class="font-medium">${message}</span>
                        <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                `;

                container.appendChild(notification);

                // Animation d'entrée
                setTimeout(() => {
                    notification.classList.remove('translate-x-full', 'opacity-0');
                }, 100);

                // Auto-suppression
                setTimeout(() => {
                    notification.classList.add('translate-x-full', 'opacity-0');
                    setTimeout(() => notification.remove(), 300);
                }, duration);
            }
            
            // Fonction simple de mise à jour
            function updateCount() {
                const checked = document.querySelectorAll('.reminder-checkbox:checked').length;
                if (counter) counter.textContent = `${checked} candidat(s) sélectionné(s)`;
                if (bulkBtn) bulkBtn.disabled = checked === 0;
                console.log('Compteur:', checked);
            }
            
            // Ajouter les événements aux checkboxes
            checkboxes.forEach(function(checkbox) {
                checkbox.addEventListener('change', function() {
                    console.log('Checkbox changée:', this.checked);
                    updateCount();
                });
            });
            
            // Bouton "Tout sélectionner"
            const selectAllBtn = document.getElementById('select-all-btn');
            if (selectAllBtn) {
                selectAllBtn.addEventListener('click', function() {
                    console.log('Tout sélectionner cliqué');
                    const allChecked = document.querySelectorAll('.reminder-checkbox:checked').length === checkboxes.length;
                    checkboxes.forEach(function(cb) {
                        cb.checked = !allChecked;
                    });
                    updateCount();
                    this.textContent = allChecked ? 'Tout sélectionner' : 'Tout désélectionner';
                });
            }

            // Gestion des filtres - soumission uniquement via le bouton
            const filterForm = document.getElementById('filter-form');
            const sortBySelect = document.getElementById('sort_by');
            const sortDirectionSelect = document.getElementById('sort_direction');

            // Soumission automatique uniquement pour les changements de tri (pas les filtres)
            if (sortBySelect) {
                sortBySelect.addEventListener('change', function() {
                    console.log('Tri par changé:', this.value);
                    filterForm.submit();
                });
            }

            if (sortDirectionSelect) {
                sortDirectionSelect.addEventListener('change', function() {
                    console.log('Direction tri changée:', this.value);
                    filterForm.submit();
                });
            }

            // Les filtres (statut et recherche) ne se soumettent que via le bouton "Appliquer les filtres"
            console.log('✅ Filtres configurés pour soumission manuelle uniquement');

            // Boutons email individuel
            const emailBtns = document.querySelectorAll('.send-individual-email');
            emailBtns.forEach(function(btn) {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Email individuel cliqué');

                    const modal = document.getElementById('individual-email-modal');
                    if (modal) {
                        const reminderId = this.dataset.reminderId;
                        const candidateName = this.dataset.candidateName;
                        const candidateEmail = this.dataset.candidateEmail;

                        // Remplir le formulaire
                        document.getElementById('individual-reminder-id').value = reminderId;
                        document.getElementById('individual-recipient').textContent = `${candidateName} (${candidateEmail})`;
                        document.getElementById('individual-subject').value = 'Rappel - Recherche d\'emploi';
                        document.getElementById('individual-message').value = `Bonjour ${candidateName},\n\nNous espérons que vous allez bien. Nous souhaitions prendre de vos nouvelles concernant votre recherche d'emploi.\n\nN'hésitez pas à nous contacter si vous avez des questions.\n\nCordialement,\nL'équipe Cyclone Placement`;

                        // Mettre à jour l'action du formulaire
                        document.getElementById('individual-email-form').action = `/admin/reminder-candidates/${reminderId}/send-email`;

                        modal.classList.remove('hidden');
                        document.body.style.overflow = 'hidden';
                        console.log('Modal ouvert');
                    } else {
                        alert('Modal non trouvé !');
                    }
                });
            });
            
            // Bouton email groupé
            if (bulkBtn) {
                bulkBtn.addEventListener('click', function() {
                    console.log('Email groupé cliqué');
                    const selectedIds = Array.from(document.querySelectorAll('.reminder-checkbox:checked')).map(cb => cb.value);

                    if (selectedIds.length === 0) {
                        alert('Veuillez sélectionner au moins un candidat');
                        return;
                    }

                    const modal = document.getElementById('bulk-email-modal');
                    if (modal) {
                        // Remplir le formulaire
                        document.getElementById('bulk-reminder-ids').value = selectedIds.join(',');
                        document.getElementById('bulk-recipients').textContent = `${selectedIds.length} candidat(s) sélectionné(s)`;
                        document.getElementById('bulk-subject').value = 'Rappel - Recherche d\'emploi';
                        document.getElementById('bulk-message').value = 'Bonjour,\n\nNous espérons que vous allez bien. Nous souhaitions prendre de vos nouvelles concernant votre recherche d\'emploi.\n\nN\'hésitez pas à nous contacter si vous avez des questions.\n\nCordialement,\nL\'équipe Cyclone Placement';

                        modal.classList.remove('hidden');
                        document.body.style.overflow = 'hidden';
                        console.log('Modal groupé ouvert');
                    } else {
                        alert('Modal groupé non trouvé !');
                    }
                });
            }
            
            // Gestionnaires pour les sélecteurs de statut
            const statusSelectors = document.querySelectorAll('.update-status');
            statusSelectors.forEach(function(selector) {
                // Sauvegarder la valeur initiale
                selector.dataset.previousValue = selector.value;

                selector.addEventListener('change', function() {
                    const reminderId = this.dataset.reminderId;
                    const newStatus = this.value;
                    const previousStatus = this.dataset.previousValue;
                    console.log('Statut changé:', reminderId, previousStatus, '→', newStatus);

                    // Appel AJAX pour mettre à jour le statut
                    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
                    console.log('Token CSRF récupéré:', csrfToken);

                    if (!csrfToken) {
                        console.error('❌ Token CSRF non trouvé !');
                        showNotification('Erreur: Token CSRF manquant', 'error');
                        return;
                    }

                    const formData = new FormData();
                    formData.append('response_status', newStatus);
                    formData.append('_token', csrfToken);

                    console.log('Envoi requête AJAX:', {
                        url: `/admin/reminder-candidates/${reminderId}/update-status`,
                        method: 'POST',
                        response_status: newStatus,
                        reminder_id: reminderId
                    });

                    fetch(`/admin/reminder-candidates/${reminderId}/update-status`, {
                        method: 'POST',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Réponse serveur changement statut:', data);

                        if (data.success) {
                            showNotification(data.message, 'success');

                            // Mettre à jour le badge visuel
                            const statusBadge = document.getElementById(`status-badge-${reminderId}`);
                            if (statusBadge) {
                                let badgeClass, badgeText;
                                switch(newStatus) {
                                    case 'yes':
                                        badgeClass = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800';
                                        badgeText = 'Oui';
                                        break;
                                    case 'no':
                                        badgeClass = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800';
                                        badgeText = 'Non';
                                        break;
                                    default:
                                        badgeClass = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800';
                                        badgeText = 'Pas de réponse';
                                }
                                statusBadge.className = badgeClass;
                                statusBadge.textContent = badgeText;
                                console.log('Badge mis à jour:', badgeText);
                            }

                            // Mettre à jour le compteur de rappels sans réponse
                            const countCell = document.getElementById(`reminder-count-cell-${reminderId}`);
                            if (countCell && data.no_response_count !== undefined) {
                                console.log('Mise à jour compteur:', data.no_response_count);
                                countCell.textContent = data.no_response_count;
                            } else {
                                console.log('Compteur non mis à jour:', {
                                    countCell: !!countCell,
                                    no_response_count: data.no_response_count
                                });
                            }

                            // Mettre à jour la valeur précédente pour les prochains changements
                            this.dataset.previousValue = newStatus;

                        } else {
                            showNotification(data.message || 'Erreur lors de la mise à jour', 'error');
                            // Remettre le sélecteur à sa valeur précédente en cas d'erreur serveur
                            this.value = this.dataset.previousValue || 'no_response';
                        }
                    })
                    .catch(error => {
                        console.error('❌ Erreur AJAX:', error);
                        showNotification('Erreur de connexion lors de la mise à jour', 'error');

                        // Remettre le sélecteur à sa valeur précédente en cas d'erreur
                        this.value = this.dataset.previousValue || 'no_response';
                    });
                });
            });

            // Bouton "Tester Email"
            const testEmailBtn = document.getElementById('test-email-btn');
            if (testEmailBtn) {
                testEmailBtn.addEventListener('click', function() {
                    console.log('Test email cliqué');
                    const modal = document.getElementById('test-email-modal');
                    if (modal) {
                        modal.classList.remove('hidden');
                        document.body.style.overflow = 'hidden';
                        console.log('Modal test ouvert');
                    }
                });
            }

            // Fermer les modals
            function closeModal(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.classList.add('hidden');
                    document.body.style.overflow = 'auto';
                }
            }

            // Gestionnaires de fermeture pour modal individuel
            document.getElementById('cancel-individual-email')?.addEventListener('click', function() {
                closeModal('individual-email-modal');
            });
            document.getElementById('close-individual-modal')?.addEventListener('click', function() {
                closeModal('individual-email-modal');
            });

            // Gestionnaires de fermeture pour modal groupé
            document.getElementById('cancel-bulk-email')?.addEventListener('click', function() {
                closeModal('bulk-email-modal');
            });
            document.getElementById('close-bulk-modal')?.addEventListener('click', function() {
                closeModal('bulk-email-modal');
            });

            // Gestionnaires de fermeture pour modal test
            document.getElementById('cancel-test-email')?.addEventListener('click', function() {
                closeModal('test-email-modal');
            });
            document.getElementById('close-test-modal')?.addEventListener('click', function() {
                closeModal('test-email-modal');
            });

            // Gestionnaire pour le formulaire d'email individuel
            const individualForm = document.getElementById('individual-email-form');
            if (individualForm) {
                individualForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.textContent;

                    // Désactiver le bouton et changer le texte
                    submitBtn.disabled = true;
                    submitBtn.textContent = 'Envoi en cours...';

                    fetch(this.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showNotification(data.message, 'success');
                            closeModal('individual-email-modal');

                            // Mettre à jour l'interface avec les données du serveur
                            const reminderId = formData.get('reminder_id');
                            updateReminderRowWithData(reminderId, data);
                        } else {
                            showNotification(data.message || 'Erreur lors de l\'envoi', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Erreur:', error);
                        showNotification('Erreur de connexion lors de l\'envoi', 'error');
                    })
                    .finally(() => {
                        // Réactiver le bouton
                        submitBtn.disabled = false;
                        submitBtn.textContent = originalText;
                    });
                });
            }

            // Gestionnaire pour le formulaire d'email groupé
            const bulkForm = document.getElementById('bulk-email-form');
            if (bulkForm) {
                bulkForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.textContent;

                    // Convertir les IDs en array pour la validation
                    const reminderIds = formData.get('reminder_ids').split(',');
                    formData.delete('reminder_ids');
                    reminderIds.forEach(id => formData.append('reminder_ids[]', id));

                    // Désactiver le bouton et changer le texte
                    submitBtn.disabled = true;
                    submitBtn.textContent = 'Envoi en cours...';

                    fetch(this.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showNotification(data.message, 'success');

                            // Afficher les erreurs s'il y en a
                            if (data.errors && data.errors.length > 0) {
                                data.errors.forEach(error => {
                                    showNotification(error, 'warning', 7000);
                                });
                            }

                            closeModal('bulk-email-modal');

                            // Décocher toutes les checkboxes
                            document.querySelectorAll('.reminder-checkbox:checked').forEach(cb => cb.checked = false);
                            updateCount();

                            // Mettre à jour les lignes concernées
                            reminderIds.forEach(id => updateReminderRow(id));
                        } else {
                            showNotification(data.message || 'Erreur lors de l\'envoi groupé', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Erreur:', error);
                        showNotification('Erreur de connexion lors de l\'envoi groupé', 'error');
                    })
                    .finally(() => {
                        // Réactiver le bouton
                        submitBtn.disabled = false;
                        submitBtn.textContent = originalText;
                    });
                });
            }

            // Gestionnaire pour le formulaire de test d'email
            const testForm = document.getElementById('test-email-form');
            if (testForm) {
                testForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.textContent;

                    // Désactiver le bouton et changer le texte
                    submitBtn.disabled = true;
                    submitBtn.textContent = 'Test en cours...';

                    fetch(this.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showNotification(data.message, 'success', 8000);
                            closeModal('test-email-modal');
                        } else {
                            showNotification(data.message || 'Erreur lors du test', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Erreur:', error);
                        showNotification('Erreur de connexion lors du test', 'error');
                    })
                    .finally(() => {
                        // Réactiver le bouton
                        submitBtn.disabled = false;
                        submitBtn.textContent = originalText;
                    });
                });
            }

            // Fonction pour mettre à jour une ligne de rappel après envoi (avec données serveur)
            function updateReminderRowWithData(reminderId, serverData) {
                console.log('Mise à jour ligne:', reminderId, serverData);

                // Mettre à jour la date du dernier rappel
                const lastReminderCell = document.getElementById(`last-reminder-cell-${reminderId}`);
                if (lastReminderCell) {
                    const now = new Date();
                    const formattedDate = now.toLocaleDateString('fr-FR') + ' ' + now.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});
                    lastReminderCell.textContent = formattedDate;
                }

                // Mettre à jour le compteur avec la valeur du serveur
                const countCell = document.getElementById(`reminder-count-cell-${reminderId}`);
                if (countCell && serverData.no_response_count !== undefined) {
                    countCell.textContent = serverData.no_response_count;
                    console.log('Compteur mis à jour:', serverData.no_response_count);
                }

                // Remettre le statut à "Pas de réponse"
                const statusBadge = document.getElementById(`status-badge-${reminderId}`);
                if (statusBadge) {
                    statusBadge.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800';
                    statusBadge.textContent = 'Pas de réponse';
                }

                // Mettre à jour le sélecteur de statut
                const statusSelector = document.querySelector(`select[data-reminder-id="${reminderId}"]`);
                if (statusSelector) {
                    statusSelector.value = 'no_response';
                }
            }

            // Fonction pour mettre à jour une ligne de rappel après envoi (version simple)
            function updateReminderRow(reminderId) {
                updateReminderRowWithData(reminderId, {});
            }

            // Initialisation
            updateCount();
            console.log('✅ SYSTÈME INITIALISÉ');
        });
    </script>
    @endpush
</x-admin-layout>
