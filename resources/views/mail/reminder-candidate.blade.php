<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirmation de recherche d'emploi</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f9f9f9;">
    <div style="padding: 40px; max-width: 600px; margin: 20px auto; background: #ffffff; border: 1px solid #ddd; border-radius: 10px; overflow: hidden; box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);">
        <!-- En-tête -->
        <div style="text-align: left; padding: 15px;">
            <h2 style="margin: 0; font-size: 20px; color: #333;">Bonjour,</h2>
        </div>

        <!-- Corps -->
        <div style="padding: 20px;">
            <p style="font-size: 16px; color: #555; line-height: 1.5; margin-bottom: 20px;">
                En tant que candidat sur notre plateforme <a href="{{ env('APP_URL') }}" style="color: #007bff; text-decoration: none;">{{ env('APP_NAME') }}</a>, nous souhaitons savoir si vous êtes toujours à la recherche d'un emploi.
            </p>
            <p style="font-size: 16px; color: #555; line-height: 1.5; margin-bottom: 20px;">
                Veuillez cliquer sur l'un des liens ci-dessous pour confirmer votre statut actuel :
            </p>

            <!-- Boutons de confirmation -->
            <div style="margin-top: 20px;">
                @if($reminder_candidate && $reminder_candidate->id)
                    <a href="{{ route('api.confirm-candidate', ['reminder_candidate_id' => $reminder_candidate->id, 'value' => 'yes']) }}"
                       style="display: inline-block; background-color: #28a745; color: #fff; text-decoration: none; padding: 10px 20px; border-radius: 5px; font-size: 14px; margin-bottom: 10px;">
                       Oui, je suis toujours à la recherche d'un emploi
                    </a>
                    <br>
                    <a href="{{ route('api.confirm-candidate', ['reminder_candidate_id' => $reminder_candidate->id, 'value' => 'no']) }}"
                       style="display: inline-block; background-color: #dc3545; color: #fff; text-decoration: none; padding: 10px 20px; border-radius: 5px; font-size: 14px;">
                       Non, je ne suis plus à la recherche d'un emploi
                    </a>
                @else
                    <p style="color: #666; font-style: italic;">
                        Ceci est un email de test. Les boutons de réponse ne sont pas disponibles.
                    </p>
                @endif
            </div>
        </div>

        <!-- Pied de page -->
        <div style="background: #f9f9f9; color: #777; text-align: center; padding: 15px; font-size: 12px; border-top: 1px solid #ddd;">
            <p style="margin: 0;">Cordialement,<br><strong>L'équipe {{ env('APP_NAME') }}</strong></p>
        </div>
    </div>
</body>
</html>
