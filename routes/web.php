<?php

use App\Http\Controllers\CandidateController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RecruterController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\Debug\DebugRino;
use App\Http\Controllers\Subscription\PlanController;
use App\Http\Controllers\Subscription\StripeWebhookController;
use App\Http\Controllers\Subscription\ProcessSubscription;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\ConversationController;
use App\Http\Controllers\Debug\DebugElie;
use App\Http\Controllers\Debug\DebugJosoa;
use App\Http\Controllers\MantisController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\PackageController;
use App\Http\Controllers\ParametreAdminMailController;
use App\Http\Controllers\ParametreCaptchaController;
use App\Http\Controllers\ParametreSmtpController;
use App\Http\Controllers\PublicationDateController;
use App\Http\Controllers\StripeApiKeyController;
use App\Http\Controllers\Subscription\CardController;
use App\Http\Controllers\Subscription\InvoiceController;
use App\Http\Controllers\TaxParameterController;
use App\Http\Controllers\ReminderCandidateController;
use App\Models\Package;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/



// Route::post('/stripe/update-stripe-product/{productId}', [PlanController::class, 'updateStripeProduct'])->name('admin.stripe-product.update');
// Route::get('/retrieve-price/{priceId}', [PlanController::class, 'retrievePrice']);
// Route::post('/update-price', [PlanController::class, 'updatePriceProduct']);
// Route::post('/update-price/{prod_id}/{old_price_id}', [PlanController::class, 'updatePriceProduct']);
// Route::post('/stripe/create-webhook', [PlanController::class, 'createWebhook']);
Route::get('/debug-elie/import-from-file-users', [DebugElie::class, 'importFromFileUsers']);
Route::get('/debug-elie/old-user-import', [DebugElie::class, 'oldUserImport']);
Route::get('/debug-elie/send-email-old-user', [DebugElie::class, 'sendEmailOldUser']);
// Route pour tester la configuration Stripe
// Route::get('/test-config-stripe', function () {
//     return [
//         'stripe_key' => config('stripe.key'),
//         'stripe_secret' => config('stripe.secret'),
//         'cashier_currency' => config('cashier.currency'),
//         'stripe_webhook_secret' => config('stripe.webhook.secret'),
//     ];
// });

// Route::get('/mantis/categories', [MantisController::class, 'getCategories']);
Route::post('/mantis/add-issue', [MantisController::class, 'addIssue']);
// Route::get('/mantis/priorities-severities', [MantisController::class, 'getListPrioritiesAndListSeverities']);



Route::get('/test-stripe-key', function () {
    return response()->json([
        'stripe_key' => config('stripe.key'),
        'stripe_secret' => config('stripe.secret'),
    ]);
});

// Route pour tester la configuration Mail
Route::get('/test-mail-config', function () {
    return [
        'mail_mailer' => config('mail.default'),
        'mail_host' => config('mail.mailers.smtp.host'),
        'mail_port' => config('mail.mailers.smtp.port'),
        'mail_username' => config('mail.mailers.smtp.username'),
        'mail_password' => config('mail.mailers.smtp.password'),
        'mail_encryption' => config('mail.mailers.smtp.encryption'),
        'mail_from_address' => config('mail.from.address'),
        'mail_from_name' => config('mail.from.name'),
    ];
});

// Route pour tester la configuration CAPTCHA
Route::get('/test-captcha-config', function () {
    return [
        'captcha_secret' => config('captcha.secret'),
        'captcha_sitekey' => config('captcha.sitekey'),
    ];
});

// Route pour tester l'email administrateur
Route::get('/test-admin-email', function () {
    return [
        'admin_email' => config('app.admin_email'),
    ];
});


Route::get('/debug-rino', [DebugRino::class, 'index'])->name('debug-rino.index');
// Route::get('/debug-josoa', [DebugJosoa::class, 'index'])->name('debug-josoa.index');
Route::get('/debug-josoa', [DebugJosoa::class, 'getAllProfessionByActivitieId']);
// Route::post('stripe/webhook', [StripeWebhookController::class, 'handleWebhook'])->name('cashier.webhook')->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class);
// Route::post('/stripe/webhook', [StripeWebhookController::class, 'handleWebhook'])->name('cashier.webhook');
// Route::post('/stripe/create-webhook', [PlanController::class, 'createWebhook'])->name('stripe-webhook.create');
// Route::get('/stripe/webhook-delete/{webhookId}', [PlanController::class, 'deleteWebhook'])->name('stripe-webhook.delete');




Route::get('/', function () {
    if (auth()->check()) {
        return redirect()->route('candidate.dashboard');
    }
    // return view('welcome');
    return app(PackageController::class)->index();
})->name('home');


// Route::get('/', function () {
//     return redirect()->route('candidate.dashboard');
// })->name('home')->middleware('auth');


Route::get('/suspenssion-page', function () {
    return view('suspenssion_page.suspenssion-page');
})->name('suspenssion_page.suspenssion-page');


Route::middleware(['auth', 'verified'])->group(function () {

    // ses routes peut etre accessible par les candidates et les recruteurs qui sont abonnés
    Route::middleware(['is.subscribed.or.candidate'])->group(function () {
        Route::post('/conversations', [ConversationController::class, 'createOrGetConversation'])->name('messages.create');
        Route::get('/conversations', [ConversationController::class, 'getUserConversations'])->name('messages.conversations');
        Route::patch('/messages/{conversationId}/read', [MessageController::class, 'markAsRead'])->name('messages.read');
        Route::get('/messages/{conversationId}', [MessageController::class, 'getMessages'])->name('messages.get');
        Route::post('/messages/send', [MessageController::class, 'sendMessage'])->name('messages.send');
        Route::get('/messages/user/{user_id}', [ConversationController::class, 'getUserMessages'])->name('messages.user');
        Route::get('/messages-count', [ConversationController::class, 'messagesCount'])->name('messages.count');
    });
});



// Route::get('/', [CandidateController::class, 'index'])->name('candidate.index');
Route::get('/inscription-candidat', [CandidateController::class, 'registerIndex'])->name('candidate.registerIndex');
Route::post('/inscription-candidat', [CandidateController::class, 'registerStore'])->name('candidate.registerStore');

// Routes pour l'inscription en étapes
Route::get('/inscription-candidat/etape-1', [CandidateController::class, 'registerStep1'])->name('candidate.registerStep1');
Route::get('/inscription-candidat/etape-2', [CandidateController::class, 'registerStep2'])->name('candidate.registerStep2');
Route::get('/inscription-candidat/etape-3', [CandidateController::class, 'registerStep3'])->name('candidate.registerStep3');
Route::get('/inscription-candidat/etape-4', [CandidateController::class, 'registerStep4'])->name('candidate.registerStep4');
Route::get('/inscription-candidat/etape-5', [CandidateController::class, 'registerStep5'])->name('candidate.registerStep5');
Route::get('/inscription-candidat/etape-6', [CandidateController::class, 'registerStep6'])->name('candidate.registerStep6');
Route::get('/inscription-candidat/etape-7', [CandidateController::class, 'registerStep7'])->name('candidate.registerStep7');

Route::get('/get-all-profession', [CandidateController::class, 'getAllProfessionByActivitieId'])->name('candidate.getAllProfession');
Route::get('/get-all-region', [CandidateController::class, 'getAllRegionOrCantonByCountryId'])->name('candidate.getAllRegion');


Route::prefix('candidats')->group(function () {

    Route::get('/', [CandidateController::class, 'index'])->name('candidate.index');
    // Route::get('/register', [CandidateController::class, 'registerIndex'])->name('candidate.registerIndex');
    // Route::post('/register', [CandidateController::class, 'registerStore'])->name('candidate.registerStore');

    Route::middleware(['auth', 'verified', 'role:candidate', 'check.suspension'])->group(function () {

        Route::get('/dashboard', [CandidateController::class, 'dashboard'])->name('candidate.dashboard');
        Route::put('/profile', [CandidateController::class, 'profileUpdate'])->name('candidate.profile.update');

        Route::get('/messages', [CandidateController::class, 'messages'])->name('candidate.messages');

        Route::get('/update-password', [CandidateController::class, 'updatePasswordIndex'])->name('candidate.update-password.index');
        Route::get('/delete-profile', [CandidateController::class, 'profileDeleteIndex'])->name('candidate.profile.delete.index');
    });

    Route::put('/update-password', [CandidateController::class, 'updatePasswordPut'])->name('candidate.update-password.put');
    Route::post('/delete-profile', [CandidateController::class, 'profileDeletePost'])->name('candidate.profile.delete.post');
});


Route::prefix('recruter')->group(function () {


    Route::middleware(['auth', 'verified', 'role:recruter', 'check.suspension'])->group(function () {

        Route::post('/get-setup-intent', [ProcessSubscription::class, 'getSetupIntent'])->name('recruter.plan.getSetupIntent');
        // recruter.plan.updateAddresse
        Route::post('/update-addresse', [ProcessSubscription::class, 'updateAddresse'])->name('recruter.plan.updateAddresse');
        Route::post('/initiate-subscription', [ProcessSubscription::class, 'initiateSubscription'])->name('recruter.plan.initiateSubscription');

        Route::post('/subscription-finish', [ProcessSubscription::class, 'subscriptionFinish'])->name('recruter.plan.subscriptionFinish');
        Route::get('/show-invoice-all', [InvoiceController::class, 'show'])->name('recruter.invoice.showAll');
        Route::get('/show-card-all', [CardController::class, 'show'])->name('recruter.card.showAll');

        Route::get('/delete-card/{paymentMethodId}', [CardController::class, 'deleteCard'])->name('recruter.card.delete');
        Route::get('/set-default-card/{customerId}/{paymentMethodId}', [CardController::class, 'setCardDefault'])->name('recruter.card.setDefault');

        Route::get('/dashboard', [RecruterController::class, 'dashboardIndex'])->name('recruter.dashboard');
        Route::put('/profile', [RecruterController::class, 'profileUpdate'])->name('recruter.profile.update');

        Route::get('/packages', [RecruterController::class, 'packageIndex'])->name('recruter.packages');
        Route::get('/liste', [RecruterController::class, 'candidateSelectedIndex'])->name('recruter.candidate-selected');
        Route::get('/candidate-selected/{candidate_id}', [RecruterController::class, 'candidateSelectedShow'])->name('recruter.candidate-selected.show');
        Route::get('/like-candidate/{candidate_id}', [RecruterController::class, 'candidateLiked'])->name('recruter.like-candidate');
        Route::get('/reset-search', [RecruterController::class, 'resetSearch'])->name('recruter.reset-search');

        Route::middleware(['is.subscribed'])->group(function () {
            Route::get('/candidate-pdf/{candidate_id}', [RecruterController::class, 'candidatePDF'])->name('recruter.candidate-pdf');
        });

        Route::get('/update-password', [CandidateController::class, 'updatePasswordIndex'])->name('recruter.update-password.index');
        Route::get('/delete-profile', [CandidateController::class, 'profileDeleteIndex'])->name('recruter.profile.delete.index');

        // Route::get('/plan/{slug}', [PlanController::class, 'show'])->name('recruter.plan.show');
        Route::get('/plan/{slug}/{new_card?}', [PlanController::class, 'show'])->name('recruter.plan.show');

        Route::get('/messages', [CandidateController::class, 'messages'])->name('recruter.messages');
    });
});

Route::get('/recruteurs', function () {
    $packages = Package::all(); // Récupère tous les packages
    return view('recruiters', compact('packages'));
})->name('recruiters');

Route::get('/abonnements', function () {

    $packages = Package::all(); // Récupère tous les packages
    return view('features', compact('packages'));

    // return view('features');
})->name('features');

Route::get('/general-conditions', function () {
    return view('general-conditions');
})->name('generalConditions');

Route::get('/faq', function () {
    return view('faq');
})->name('faq');

Route::middleware(['auth', 'verified',])->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::get('/contact', [ContactController::class, 'index'])->name('contact');
    Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');
});

Route::middleware(['auth', 'role:admin'])->group(function () {
    // route fot admin now not have a middleware
    Route::prefix('admin')->group(function () {
        Route::get('/', [AdminController::class, 'index'])->name('admin.index');
        Route::get('/recruteur/{id}', [AdminController::class, 'showRecruiter'])->name('admin.recruiters.show');
        Route::get('/candidats/{f?}', [AdminController::class, 'getAllCandidats'])->name('admin.candidates');
        Route::get('/candidat/{id}', [AdminController::class, 'showCandidate'])->name('admin.candidate.show');
        //  searchRecruiter
        // Route::post('/recherche-recruteur', [AdminController::class, 'searchRecruiter'])->name('admin.recruiters.search');
        Route::get('/recherche-recruteur', [AdminController::class, 'searchRecruiter'])->name('admin.recruiters.search');
        Route::get('/recherche-candidat', [AdminController::class, 'searchCandidat'])->name('admin.candidat.search');

        Route::get('/recruteurs/{f?}', [AdminController::class, 'getAllRecruiter'])->name('admin.recruiters');

        // 'admin.recruiters.suspend'
        Route::get('/recruteur/{id}/suspend', [AdminController::class, 'suspendRecruiter'])->name('admin.recruiters.suspend');
        // 'admin.recruiters.unsuspend'
        Route::get('/recruteur/{id}/unsuspend', [AdminController::class, 'unsuspendRecruiter'])->name('admin.recruiters.unsuspend');

        Route::get('/activite/{f?}', [AdminController::class, 'showActivities'])->name('admin.activities');
        Route::post('/add-activite', [AdminController::class, 'addActivity'])->name('admin.add-activities');
        Route::put('/update-activite/{id}', [AdminController::class, 'updateActivity'])->name('admin.update-activities');
        Route::delete('/activite/{id}', [AdminController::class, 'deleteActivity'])->name('admin.delete-activities');
        Route::get('/recherche-activite', [AdminController::class, 'searchActivity'])->name('admin.search-activities');
        Route::post('/activite/{id}/toggle', [AdminController::class, 'enableDisableActivity']);

        Route::get('/profession/{f?}', [AdminController::class, 'proffessionIndex'])->name('admin.profession');
        Route::post('/add-profession', [AdminController::class, 'proffessionStore'])->name('admin.proffession.store');
        Route::post('/update-profession/{id}', [AdminController::class, 'proffessionUpdate'])->name('admin.proffession.update');
        Route::post('/toggle-profession-is-active/{id}', [AdminController::class, 'proffessionToggleIsActive'])->name('admin.proffession.toggle-is-active');
        Route::delete('/delete-profession/{id}', [AdminController::class, 'proffessionDelete'])->name('admin.proffession.delete');
        // proffessionSearch
        // Route::post('/recherche-profession', [AdminController::class, 'proffessionSearch'])->name('admin.proffession.search');
        Route::get('/recherche-profession', [AdminController::class, 'professionSearch'])->name('admin.proffession.search');

        Route::get('/stripe/products', [PlanController::class, 'listProduct'])->name('admin.stripe-product.index');
        Route::post('/stripe/create-product', [PlanController::class, 'createStripeProduct'])->name('admin.create-product');
        Route::delete('/stripe/delete-stripe-product/{product_id}', [PlanController::class, 'deleteStripeProduct'])->name('stripe-product.delete');

        Route::get('/stripe/list-webhook', [PlanController::class, 'webhookList'])->name('stripe-webhook.list');
        Route::post('/stripe/create-webhook', [PlanController::class, 'createWebhook'])->name('stripe-webhook.create');
        // Route::get('/stripe/webhook-delete/{webhookId}', [PlanController::class, 'deleteWebhook'])->name('stripe-webhook.delete');
        Route::post('/stripe/webhook-delete', [PlanController::class, 'deleteWebhook'])->name('stripe-webhook.delete');
        Route::put('/webhooks-update/{webhookId}', [PlanController::class, 'updateWebhookUrl'])->name('stripe-webhook.update');
        Route::get('/webhooks/{webhookId}/edit', [PlanController::class, 'editWebhook'])->name('stripe-webhook.edit');


        Route::get('/stripe/date-publication', [PublicationDateController::class, 'index'])->name('stripe-webhook.date-publication');
        Route::post('/stripe/add-date-publication', [PublicationDateController::class, 'store'])->name('stripe-webhook.add-date-publication');
        Route::delete('/stripe/delete-date-publication/{id}', [PublicationDateController::class, 'destroy'])->name('stripe-webhook.delete-date-publication');
        Route::get('/date-publication/{name}/edit', [PublicationDateController::class, 'edit'])->name('stripe-webhook.edit-date-publication');
        Route::post('/date-publication/update', [PublicationDateController::class, 'update'])->name('stripe-webhook.update-date-publication');

        Route::get('/stripe/parametre-tax', [TaxParameterController::class, 'index'])->name('stripe-webhook.parametre-tax');
        Route::post('/stripe/parametre-tax/update', [TaxParameterController::class, 'update'])->name('stripe-webhook.parametre-tax.update');

        Route::get('/stripe/stripe-api-key', [StripeApiKeyController::class, 'index'])->name('stripe-webhook.stripe-api-key');
        Route::post('/stripe-config/update', [StripeApiKeyController::class, 'update'])->name('stripe-config.update');

        Route::get('/parametre-smtp', [ParametreSmtpController::class, 'index'])->name('parametre-smtp');
        Route::post('/parametre-smtp/update', [ParametreSmtpController::class, 'update'])->name('parametre-smtp.update');

        Route::get('/parametre-captcha', [ParametreCaptchaController::class, 'index'])->name('parametre-captcha');
        Route::post('/parametre-captcha/update', [ParametreCaptchaController::class, 'update'])->name('parametre-captcha.update');

        Route::get('/admin-mail', [ParametreAdminMailController::class, 'index'])->name('admin-mail');
        Route::post('/admin-mail/update', [ParametreAdminMailController::class, 'update'])->name('admin-mail.update');

        Route::get('/avis', [AdminController::class, 'getAllAvis'])->name('admin.avis');

        // Reminder Candidates Routes
        Route::get('/reminder-candidates', [ReminderCandidateController::class, 'index'])->name('admin.reminder-candidates.index');
        Route::post('/reminder-candidates/{reminder}/send-email', [ReminderCandidateController::class, 'sendIndividualEmail'])->name('admin.reminder-candidates.send-email');
        Route::post('/reminder-candidates/send-bulk-emails', [ReminderCandidateController::class, 'sendBulkEmails'])->name('admin.reminder-candidates.send-bulk-emails');
        Route::post('/reminder-candidates/{reminder}/update-status', [ReminderCandidateController::class, 'updateResponseStatus'])->name('admin.reminder-candidates.update-status');
        Route::get('/reminder-candidates/export', [ReminderCandidateController::class, 'exportCsv'])->name('admin.reminder-candidates.export');
        Route::post('/reminder-candidates/test-email', [ReminderCandidateController::class, 'testEmail'])->name('admin.reminder-candidates.test-email');

        // Packages Routes
        Route::get('/packages', [PackageController::class, 'show'])->name('admin.packages.show');
        Route::get('/packages/create', [PackageController::class, 'create'])->name('admin.packages.create');
        Route::post('/packages', [PackageController::class, 'store'])->name('admin.packages.store');
        Route::get('/packages/{package}/edit', [PackageController::class, 'edit'])->name('admin.packages.edit');
        Route::put('/packages/{package}', [PackageController::class, 'update'])->name('admin.packages.update');
        Route::delete('/packages/{package}', [PackageController::class, 'destroy'])->name('admin.packages.destroy');
    });
});

// Route sécurisée pour exécuter la commande ReminderCandidateCommand via CRON
Route::get('/cron/reminder-candidate/{token}', function ($token) {
    // Vérifier que le token correspond à celui défini dans .env
    if ($token !== config('app.cron_token')) {
        abort(403, 'Unauthorized');
    }

    // Exécuter la commande
    Artisan::call('app:reminder-candidate');

    return response()->json(['status' => 'success', 'message' => 'Reminder candidate command executed successfully']);
})->name('cron.reminder-candidate');

require __DIR__ . '/auth.php';
