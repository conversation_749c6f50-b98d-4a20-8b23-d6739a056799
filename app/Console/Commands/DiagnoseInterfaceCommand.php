<?php

namespace App\Console\Commands;

use App\Models\ReminderCandidate;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Route;

class DiagnoseInterfaceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:diagnose-interface';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Diagnostic approfondi de l\'interface des rappels candidats';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 DIAGNOSTIC APPROFONDI DE L\'INTERFACE');
        $this->info('=========================================');
        
        // 1. Vérifier les routes
        $this->checkRoutes();
        
        // 2. Vérifier les données
        $this->checkData();
        
        // 3. Vérifier la vue
        $this->checkView();
        
        // 4. C<PERSON>er une page de diagnostic
        $this->createDiagnosticPage();
        
        return 0;
    }
    
    private function checkRoutes()
    {
        $this->info('');
        $this->info('🛣️ Vérification des routes:');
        
        $routes = [
            'admin.reminder-candidates.index',
            'admin.reminder-candidates.send-email',
            'admin.reminder-candidates.send-bulk-emails',
            'admin.reminder-candidates.update-status',
            'admin.reminder-candidates.test-email'
        ];
        
        foreach ($routes as $routeName) {
            try {
                if ($routeName === 'admin.reminder-candidates.send-email') {
                    $url = route($routeName, ['reminder' => 'test']);
                } elseif ($routeName === 'admin.reminder-candidates.update-status') {
                    $url = route($routeName, ['reminder' => 'test']);
                } else {
                    $url = route($routeName);
                }
                $this->info("   ✅ {$routeName}: {$url}");
            } catch (\Exception $e) {
                $this->error("   ❌ {$routeName}: " . $e->getMessage());
            }
        }
    }
    
    private function checkData()
    {
        $this->info('');
        $this->info('📊 Vérification des données:');
        
        $reminders = ReminderCandidate::with('user')->get();
        $this->info("   - Rappels en base: {$reminders->count()}");
        
        if ($reminders->count() > 0) {
            $reminder = $reminders->first();
            $this->info("   - Premier rappel ID: {$reminder->id}");
            $this->info("   - Utilisateur associé: " . ($reminder->user ? $reminder->user->email : 'AUCUN'));
            $this->info("   - Statut: {$reminder->response_status}");
        }
    }
    
    private function checkView()
    {
        $this->info('');
        $this->info('👁️ Vérification de la vue:');
        
        $viewPath = resource_path('views/admin/reminder-candidates/index.blade.php');
        if (file_exists($viewPath)) {
            $this->info("   ✅ Fichier vue existe: {$viewPath}");
            
            $content = file_get_contents($viewPath);
            
            // Vérifier les éléments critiques
            $checks = [
                'reminder-checkbox' => 'Checkboxes candidats',
                'selected-count' => 'Compteur de sélection',
                'bulk-email-btn' => 'Bouton email groupé',
                'select-all-btn' => 'Bouton tout sélectionner',
                'send-individual-email' => 'Boutons email individuel',
                'individual-email-modal' => 'Modal email individuel',
                'bulk-email-modal' => 'Modal email groupé'
            ];
            
            foreach ($checks as $element => $description) {
                if (strpos($content, $element) !== false) {
                    $this->info("   ✅ {$description}: trouvé");
                } else {
                    $this->error("   ❌ {$description}: MANQUANT");
                }
            }
            
        } else {
            $this->error("   ❌ Fichier vue non trouvé");
        }
    }
    
    private function createDiagnosticPage()
    {
        $this->info('');
        $this->info('🔧 Création d\'une page de diagnostic...');
        
        $diagnosticHtml = $this->generateDiagnosticHtml();
        
        $diagnosticPath = public_path('diagnostic-interface.html');
        file_put_contents($diagnosticPath, $diagnosticHtml);
        
        $this->info("   ✅ Page de diagnostic créée: {$diagnosticPath}");
        $this->info("   🌐 URL: http://localhost:8000/diagnostic-interface.html");
        $this->info('');
        $this->info('📋 Cette page va:');
        $this->info('   - Analyser la structure HTML en temps réel');
        $this->info('   - Tester les sélecteurs JavaScript');
        $this->info('   - Identifier les éléments manquants');
        $this->info('   - Afficher les erreurs JavaScript');
    }
    
    private function generateDiagnosticHtml()
    {
        return '<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic Interface Rappels</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .diagnostic-item { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d1fae5; border: 1px solid #10b981; }
        .error { background-color: #fee2e2; border: 1px solid #ef4444; }
        .warning { background-color: #fef3c7; border: 1px solid #f59e0b; }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">🔍 Diagnostic Interface Rappels Candidats</h1>
        
        <div id="diagnostic-results" class="space-y-4">
            <div class="bg-white p-4 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Initialisation du diagnostic...</h2>
                <div id="loading">⏳ Chargement en cours...</div>
            </div>
        </div>
        
        <div class="mt-8 bg-white p-4 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">Actions de test</h2>
            <div class="space-x-4">
                <button id="test-selectors" class="px-4 py-2 bg-blue-600 text-white rounded">Tester les sélecteurs</button>
                <button id="simulate-clicks" class="px-4 py-2 bg-green-600 text-white rounded">Simuler les clics</button>
                <button id="check-events" class="px-4 py-2 bg-purple-600 text-white rounded">Vérifier les événements</button>
            </div>
        </div>
        
        <div class="mt-8 bg-white p-4 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">Console de debug</h2>
            <div id="debug-console" class="bg-black text-green-400 p-4 rounded font-mono text-sm h-64 overflow-y-auto"></div>
        </div>
    </div>

    <script>
        let debugConsole = document.getElementById("debug-console");
        let diagnosticResults = document.getElementById("diagnostic-results");
        
        function log(message, type = "info") {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === "error" ? "text-red-400" : type === "warning" ? "text-yellow-400" : "text-green-400";
            debugConsole.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            debugConsole.scrollTop = debugConsole.scrollHeight;
            console.log(message);
        }
        
        function addDiagnosticItem(title, status, details = "") {
            const statusClass = status === "success" ? "success" : status === "error" ? "error" : "warning";
            const statusIcon = status === "success" ? "✅" : status === "error" ? "❌" : "⚠️";
            
            const item = document.createElement("div");
            item.className = `diagnostic-item ${statusClass}`;
            item.innerHTML = `<strong>${statusIcon} ${title}</strong><br>${details}`;
            diagnosticResults.appendChild(item);
        }
        
        function runDiagnostic() {
            log("🚀 Démarrage du diagnostic");
            document.getElementById("loading").style.display = "none";
            
            // Test 1: Vérifier si on est sur la bonne page
            const currentUrl = window.location.href;
            if (currentUrl.includes("reminder-candidates")) {
                addDiagnosticItem("Page correcte", "success", "Vous êtes sur la page des rappels candidats");
            } else {
                addDiagnosticItem("Page incorrecte", "warning", `URL actuelle: ${currentUrl}<br>Naviguez vers /admin/reminder-candidates`);
            }
            
            // Test 2: Vérifier les éléments critiques
            const criticalElements = [
                { selector: ".reminder-checkbox", name: "Checkboxes candidats" },
                { selector: "#selected-count", name: "Compteur de sélection" },
                { selector: "#bulk-email-btn", name: "Bouton email groupé" },
                { selector: "#select-all-btn", name: "Bouton tout sélectionner" },
                { selector: ".send-individual-email", name: "Boutons email individuel" },
                { selector: "#individual-email-modal", name: "Modal email individuel" },
                { selector: "#bulk-email-modal", name: "Modal email groupé" }
            ];
            
            criticalElements.forEach(element => {
                const found = document.querySelectorAll(element.selector);
                if (found.length > 0) {
                    addDiagnosticItem(element.name, "success", `${found.length} élément(s) trouvé(s)`);
                    log(`✅ ${element.name}: ${found.length} trouvé(s)`);
                } else {
                    addDiagnosticItem(element.name, "error", "Aucun élément trouvé");
                    log(`❌ ${element.name}: MANQUANT`, "error");
                }
            });
            
            // Test 3: Vérifier les erreurs JavaScript
            window.addEventListener("error", function(e) {
                log(`❌ Erreur JS: ${e.message} (${e.filename}:${e.lineno})`, "error");
                addDiagnosticItem("Erreur JavaScript détectée", "error", `${e.message} à la ligne ${e.lineno}`);
            });
            
            // Test 4: Vérifier les frameworks CSS/JS
            const frameworks = [
                { name: "Tailwind CSS", test: () => getComputedStyle(document.body).getPropertyValue("--tw-bg-opacity") },
                { name: "Alpine.js", test: () => window.Alpine },
                { name: "jQuery", test: () => window.jQuery }
            ];
            
            frameworks.forEach(fw => {
                if (fw.test()) {
                    addDiagnosticItem(`Framework ${fw.name}`, "success", "Chargé et disponible");
                } else {
                    addDiagnosticItem(`Framework ${fw.name}`, "warning", "Non détecté");
                }
            });
        }
        
        // Gestionnaires pour les boutons de test
        document.getElementById("test-selectors").addEventListener("click", function() {
            log("🧪 Test des sélecteurs...");
            const selectors = [".reminder-checkbox", "#bulk-email-btn", "#selected-count"];
            selectors.forEach(sel => {
                const elements = document.querySelectorAll(sel);
                log(`Sélecteur ${sel}: ${elements.length} élément(s)`);
            });
        });
        
        document.getElementById("simulate-clicks").addEventListener("click", function() {
            log("🖱️ Simulation des clics...");
            const checkbox = document.querySelector(".reminder-checkbox");
            if (checkbox) {
                checkbox.click();
                log("✅ Clic simulé sur checkbox");
            } else {
                log("❌ Aucune checkbox trouvée", "error");
            }
        });
        
        document.getElementById("check-events").addEventListener("click", function() {
            log("🔍 Vérification des événements...");
            const elements = document.querySelectorAll(".reminder-checkbox, #bulk-email-btn, .send-individual-email");
            elements.forEach((el, i) => {
                const events = getEventListeners ? getEventListeners(el) : "Non disponible";
                log(`Élément ${i}: ${Object.keys(events).length || 0} événement(s)`);
            });
        });
        
        // Démarrer le diagnostic
        setTimeout(runDiagnostic, 1000);
    </script>
</body>
</html>';
    }
}
