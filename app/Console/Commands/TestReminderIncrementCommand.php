<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ReminderCandidate;
use App\Models\User;

class TestReminderIncrementCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-reminder-increment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test de l\'incrémentation des compteurs de rappels';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 TEST DE L\'INCRÉMENTATION DES COMPTEURS');
        $this->info('==========================================');
        
        // Trouver un reminder existant pour tester
        $reminder = ReminderCandidate::with('user')->first();
        
        if (!$reminder) {
            $this->error('❌ Aucun reminder trouvé en base pour tester');
            $this->info('Créez d\'abord des rappels candidats ou exécutez: php artisan app:reminder-candidate');
            return 1;
        }
        
        $this->info("📊 Test avec le candidat: {$reminder->user->email}");
        $this->info('');
        
        // Afficher l'état initial
        $this->info('🔍 ÉTAT INITIAL:');
        $this->info("- reminder_count: {$reminder->reminder_count}");
        $this->info("- no_response_reminder_count: {$reminder->no_response_reminder_count}");
        $this->info("- response_status: {$reminder->response_status}");
        $this->info("- last_reminder_sent_at: " . ($reminder->last_reminder_sent_at ? $reminder->last_reminder_sent_at->format('Y-m-d H:i:s') : 'null'));
        
        $this->info('');
        $this->info('🚀 TEST D\'INCRÉMENTATION...');
        
        // Tester l'incrémentation
        $oldReminderCount = $reminder->reminder_count ?? 0;
        $oldNoResponseCount = $reminder->no_response_reminder_count ?? 0;
        
        try {
            $reminder->incrementNoResponseReminderCount();
            
            $this->info('✅ Méthode incrementNoResponseReminderCount() exécutée');
            
            // Recharger depuis la base
            $reminder->refresh();
            
            $this->info('');
            $this->info('🔍 ÉTAT APRÈS INCRÉMENTATION:');
            $this->info("- reminder_count: {$reminder->reminder_count} (était: {$oldReminderCount})");
            $this->info("- no_response_reminder_count: {$reminder->no_response_reminder_count} (était: {$oldNoResponseCount})");
            $this->info("- response_status: {$reminder->response_status}");
            $this->info("- last_reminder_sent_at: " . $reminder->last_reminder_sent_at->format('Y-m-d H:i:s'));
            
            $this->info('');
            $this->info('📊 VÉRIFICATIONS:');
            
            // Vérifier que reminder_count a été incrémenté
            if ($reminder->reminder_count == $oldReminderCount + 1) {
                $this->info('✅ reminder_count correctement incrémenté (+1)');
            } else {
                $this->error("❌ reminder_count incorrect. Attendu: " . ($oldReminderCount + 1) . ", Obtenu: {$reminder->reminder_count}");
            }
            
            // Vérifier que no_response_reminder_count a été incrémenté
            if ($reminder->no_response_reminder_count == $oldNoResponseCount + 1) {
                $this->info('✅ no_response_reminder_count correctement incrémenté (+1)');
            } else {
                $this->error("❌ no_response_reminder_count incorrect. Attendu: " . ($oldNoResponseCount + 1) . ", Obtenu: {$reminder->no_response_reminder_count}");
            }
            
            // Vérifier que le statut est "no_response"
            if ($reminder->response_status === 'no_response') {
                $this->info('✅ response_status correctement mis à "no_response"');
            } else {
                $this->error("❌ response_status incorrect. Attendu: no_response, Obtenu: {$reminder->response_status}");
            }
            
            // Vérifier que last_reminder_sent_at a été mis à jour
            if ($reminder->last_reminder_sent_at && $reminder->last_reminder_sent_at->diffInMinutes(now()) < 1) {
                $this->info('✅ last_reminder_sent_at correctement mis à jour');
            } else {
                $this->error('❌ last_reminder_sent_at non mis à jour');
            }
            
            $this->info('');
            $this->info('🧪 TEST DE RÉPONSE...');
            
            // Tester la méthode markAsResponded
            $reminder->markAsResponded('yes');
            $reminder->refresh();
            
            $this->info('');
            $this->info('🔍 ÉTAT APRÈS RÉPONSE "OUI":');
            $this->info("- no_response_reminder_count: {$reminder->no_response_reminder_count}");
            $this->info("- response_status: {$reminder->response_status}");
            
            // Vérifier que no_response_reminder_count a été remis à 0
            if ($reminder->no_response_reminder_count == 0) {
                $this->info('✅ no_response_reminder_count correctement remis à 0');
            } else {
                $this->error("❌ no_response_reminder_count non remis à 0. Obtenu: {$reminder->no_response_reminder_count}");
            }
            
            // Vérifier que le statut est "yes"
            if ($reminder->response_status === 'yes') {
                $this->info('✅ response_status correctement mis à "yes"');
            } else {
                $this->error("❌ response_status incorrect. Attendu: yes, Obtenu: {$reminder->response_status}");
            }
            
            $this->info('');
            $this->info('🎯 RÉSUMÉ DU TEST:');
            $this->info('================');
            $this->info('✅ incrementNoResponseReminderCount() fonctionne');
            $this->info('✅ markAsResponded() fonctionne');
            $this->info('✅ Tous les champs sont correctement mis à jour');
            $this->info('✅ La logique métier est cohérente');
            
            $this->info('');
            $this->info('🚀 PROCHAINES ÉTAPES:');
            $this->info('1. Testez l\'interface web: http://localhost:8000/admin/reminder-candidates');
            $this->info('2. Envoyez un email individuel et vérifiez l\'incrémentation');
            $this->info('3. Changez le statut via le sélecteur et vérifiez la remise à 0');
            $this->info('4. Testez l\'email groupé');
            
        } catch (\Exception $e) {
            $this->error('❌ ERREUR lors du test: ' . $e->getMessage());
            $this->error('Trace: ' . $e->getTraceAsString());
            return 1;
        }
        
        return 0;
    }
}
