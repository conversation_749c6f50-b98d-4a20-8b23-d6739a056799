<?php

namespace App\Console\Commands;

use App\Mail\ReminderCandidateMail;
use App\Models\ReminderCandidate;
use App\Models\User;
use App\Models\ConfigGlobalApp;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class TestEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-email {email?} {--diagnose : Mode diagnostic complet} {--real : Utiliser de vraies données}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Teste l\'envoi d\'emails de rappel candidat avec diagnostic complet';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email') ?? '<EMAIL>';
        $diagnose = $this->option('diagnose');
        $useReal = $this->option('real');

        $this->info('=== DIAGNOSTIC COMPLET DU SYSTÈME EMAIL ===');

        if ($diagnose) {
            $this->runFullDiagnostic();
        }

        // Afficher la configuration actuelle
        $this->displayMailConfig();

        // Vérifier la base de données
        $this->checkDatabaseConfig();

        // Créer un reminder candidat de test
        if ($useReal) {
            $testReminder = ReminderCandidate::first();
            if (!$testReminder) {
                $this->error('❌ Aucun reminder candidat trouvé en base de données');
                return;
            }
            $this->info("📋 Utilisation du reminder réel ID: {$testReminder->id}");
        } else {
            $testReminder = new ReminderCandidate();
            $testReminder->id = 'test-' . uniqid();
            $testReminder->user_id = 'test-user-id';
            $testReminder->search_work = null;
            $testReminder->last_reminder_sent_at = now();
            $testReminder->reminder_count = 1;
            $testReminder->response_status = 'no_response';
            $this->info("📋 Utilisation d'un reminder de test");
        }

        $this->info("Tentative d'envoi d'email de test à : {$email}");

        try {
            // Test d'envoi
            Mail::to($email)->send(new ReminderCandidateMail($testReminder));
            $this->info('✅ Email envoyé avec succès !');

        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de l\'envoi de l\'email :');
            $this->error($e->getMessage());

            // Log l'erreur pour plus de détails
            Log::error('Erreur envoi email test', [
                'email' => $email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Diagnostic approfondi en cas d'erreur
            $this->runErrorDiagnostic($e);
        }
    }

    /**
     * Diagnostic complet du système
     */
    private function runFullDiagnostic()
    {
        $this->info('🔍 DIAGNOSTIC COMPLET');
        $this->info('');

        // 1. Vérifier les providers
        $this->info('1. Vérification des Service Providers :');
        $providers = config('app.providers');
        $mailProviders = array_filter($providers, function($provider) {
            return strpos($provider, 'Mail') !== false ||
                   strpos($provider, 'Config') !== false ||
                   strpos($provider, 'GlobalConfig') !== false;
        });

        foreach ($mailProviders as $provider) {
            $this->info("   - {$provider}");
        }
        $this->info('');

        // 2. Vérifier les connexions de base de données
        $this->info('2. Vérification des connexions de base de données :');
        try {
            $configs = DB::table('config_global_apps')->get();
            $this->info("   ✅ Connexion MongoDB réussie ({$configs->count()} configurations trouvées)");
        } catch (\Exception $e) {
            $this->error("   ❌ Erreur de connexion MongoDB : " . $e->getMessage());
        }
        $this->info('');
    }

    /**
     * Vérifier la configuration de la base de données
     */
    private function checkDatabaseConfig()
    {
        $this->info('3. Vérification de la configuration mail en base de données :');

        try {
            $mailConfig = ConfigGlobalApp::where('name', 'mail_config')->first();

            if ($mailConfig) {
                $config = json_decode($mailConfig->value, true);
                $this->info('   ✅ Configuration mail trouvée en base :');
                $this->info("      - Host: {$config['MAIL_HOST']}");
                $this->info("      - Port: {$config['MAIL_PORT']}");
                $this->info("      - Username: {$config['MAIL_USERNAME']}");
                $this->info("      - Encryption: {$config['MAIL_ENCRYPTION']}");
                $this->info("      - From: {$config['MAIL_FROM_ADDRESS']}");
            } else {
                $this->error('   ❌ Configuration mail non trouvée en base de données');
            }
        } catch (\Exception $e) {
            $this->error('   ❌ Erreur lors de la lecture de la configuration : ' . $e->getMessage());
        }
        $this->info('');
    }

    /**
     * Diagnostic d'erreur approfondi
     */
    private function runErrorDiagnostic(\Exception $e)
    {
        $this->info('');
        $this->info('🔍 DIAGNOSTIC D\'ERREUR APPROFONDI :');

        $errorMessage = $e->getMessage();

        // Analyser le type d'erreur
        if (strpos($errorMessage, 'Connection refused') !== false) {
            $this->error('❌ PROBLÈME DE CONNEXION SMTP');
            $this->info('💡 Solutions possibles :');
            $this->info('   - Vérifiez que le serveur SMTP est accessible');
            $this->info('   - Vérifiez le host et le port SMTP');
            $this->info('   - Vérifiez les règles de firewall');

        } elseif (strpos($errorMessage, 'Authentication failed') !== false) {
            $this->error('❌ PROBLÈME D\'AUTHENTIFICATION');
            $this->info('💡 Solutions possibles :');
            $this->info('   - Vérifiez le nom d\'utilisateur et mot de passe SMTP');
            $this->info('   - Vérifiez que l\'authentification est activée sur le serveur');

        } elseif (strpos($errorMessage, 'SSL') !== false || strpos($errorMessage, 'TLS') !== false) {
            $this->error('❌ PROBLÈME DE CHIFFREMENT SSL/TLS');
            $this->info('💡 Solutions possibles :');
            $this->info('   - Vérifiez le type de chiffrement (ssl/tls)');
            $this->info('   - Vérifiez le port (465 pour SSL, 587 pour TLS)');

        } else {
            $this->error('❌ ERREUR GÉNÉRIQUE');
            $this->info('💡 Erreur complète : ' . $errorMessage);
        }

        $this->info('');
        $this->info('📋 ACTIONS RECOMMANDÉES :');
        $this->info('1. Vérifiez la configuration SMTP dans la base de données');
        $this->info('2. Testez la connexion SMTP manuellement');
        $this->info('3. Vérifiez les logs Laravel : storage/logs/laravel.log');
        $this->info('4. Contactez votre fournisseur d\'email si nécessaire');
    }

    /**
     * Affiche la configuration mail actuelle
     */
    private function displayMailConfig()
    {
        $this->info('📧 Configuration mail Laravel actuelle :');
        $this->info('- Mailer: ' . (Config::get('mail.default') ?? 'NON DÉFINI'));
        $this->info('- Host: ' . (Config::get('mail.mailers.smtp.host') ?? 'NON DÉFINI'));
        $this->info('- Port: ' . (Config::get('mail.mailers.smtp.port') ?? 'NON DÉFINI'));
        $this->info('- Encryption: ' . (Config::get('mail.mailers.smtp.encryption') ?? 'NON DÉFINI'));
        $this->info('- Username: ' . (Config::get('mail.mailers.smtp.username') ?? 'NON DÉFINI'));
        $this->info('- From Address: ' . (Config::get('mail.from.address') ?? 'NON DÉFINI'));
        $this->info('- From Name: ' . (Config::get('mail.from.name') ?? 'NON DÉFINI'));
        $this->info('');
    }
}
