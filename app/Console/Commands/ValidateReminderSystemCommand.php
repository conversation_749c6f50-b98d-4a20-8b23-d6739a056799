<?php

namespace App\Console\Commands;

use App\Mail\ReminderCandidateMail;
use App\Models\ReminderCandidate;
use App\Models\User;
use App\Models\Role;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class ValidateReminderSystemCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:validate-reminder-system {--email= : Email pour les tests}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Valide complètement le système de rappels candidats';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $testEmail = $this->option('email') ?? '<EMAIL>';
        
        $this->info('🔍 VALIDATION COMPLÈTE DU SYSTÈME DE RAPPELS CANDIDATS');
        $this->info('=======================================================');
        
        $allTestsPassed = true;
        
        // Test 1: Configuration email
        $this->info('');
        $this->info('📧 Test 1: Configuration email');
        if ($this->testEmailConfiguration()) {
            $this->info('   ✅ Configuration email OK');
        } else {
            $this->error('   ❌ Problème de configuration email');
            $allTestsPassed = false;
        }
        
        // Test 2: Envoi d'email de test
        $this->info('');
        $this->info('📨 Test 2: Envoi d\'email de test');
        if ($this->testEmailSending($testEmail)) {
            $this->info('   ✅ Envoi d\'email OK');
        } else {
            $this->error('   ❌ Problème d\'envoi d\'email');
            $allTestsPassed = false;
        }
        
        // Test 3: Modèle ReminderCandidate
        $this->info('');
        $this->info('🗃️ Test 3: Modèle ReminderCandidate');
        if ($this->testReminderCandidateModel()) {
            $this->info('   ✅ Modèle ReminderCandidate OK');
        } else {
            $this->error('   ❌ Problème avec le modèle ReminderCandidate');
            $allTestsPassed = false;
        }
        
        // Test 4: Routes de confirmation
        $this->info('');
        $this->info('🛣️ Test 4: Routes de confirmation');
        if ($this->testConfirmationRoutes()) {
            $this->info('   ✅ Routes de confirmation OK');
        } else {
            $this->error('   ❌ Problème avec les routes de confirmation');
            $allTestsPassed = false;
        }
        
        // Test 5: Template email
        $this->info('');
        $this->info('📄 Test 5: Template email');
        if ($this->testEmailTemplate()) {
            $this->info('   ✅ Template email OK');
        } else {
            $this->error('   ❌ Problème avec le template email');
            $allTestsPassed = false;
        }
        
        // Résultat final
        $this->info('');
        $this->info('=======================================================');
        if ($allTestsPassed) {
            $this->info('🎉 TOUS LES TESTS SONT PASSÉS ! Le système fonctionne correctement.');
        } else {
            $this->error('❌ CERTAINS TESTS ONT ÉCHOUÉ. Vérifiez les erreurs ci-dessus.');
        }
        
        return $allTestsPassed ? 0 : 1;
    }
    
    private function testEmailConfiguration(): bool
    {
        try {
            $mailer = config('mail.default');
            $host = config('mail.mailers.smtp.host');
            $port = config('mail.mailers.smtp.port');
            $username = config('mail.mailers.smtp.username');
            $fromAddress = config('mail.from.address');
            
            return !empty($mailer) && !empty($host) && !empty($port) && 
                   !empty($username) && !empty($fromAddress);
        } catch (\Exception $e) {
            $this->error("      Erreur: " . $e->getMessage());
            return false;
        }
    }
    
    private function testEmailSending(string $email): bool
    {
        try {
            $testReminder = new ReminderCandidate();
            $testReminder->id = 'test-' . uniqid();
            $testReminder->user_id = 'test-user-id';
            $testReminder->search_work = null;
            $testReminder->last_reminder_sent_at = now();
            $testReminder->reminder_count = 1;
            $testReminder->response_status = 'no_response';
            
            Mail::to($email)->send(new ReminderCandidateMail($testReminder));
            return true;
        } catch (\Exception $e) {
            $this->error("      Erreur: " . $e->getMessage());
            return false;
        }
    }
    
    private function testReminderCandidateModel(): bool
    {
        try {
            // Test de création
            $reminder = new ReminderCandidate();
            $reminder->user_id = 'test-user';
            $reminder->search_work = null;
            $reminder->last_reminder_sent_at = now();
            $reminder->reminder_count = 0;
            $reminder->response_status = 'no_response';
            
            // Test des méthodes
            $formattedStatus = $reminder->getFormattedResponseStatusAttribute();
            $badgeColor = $reminder->getStatusBadgeColorAttribute();
            
            return !empty($formattedStatus) && !empty($badgeColor);
        } catch (\Exception $e) {
            $this->error("      Erreur: " . $e->getMessage());
            return false;
        }
    }
    
    private function testConfirmationRoutes(): bool
    {
        try {
            // Vérifier que les routes existent
            $yesRoute = route('api.confirm-candidate', ['reminder_candidate_id' => 'test', 'value' => 'yes']);
            $noRoute = route('api.confirm-candidate', ['reminder_candidate_id' => 'test', 'value' => 'no']);
            
            return !empty($yesRoute) && !empty($noRoute);
        } catch (\Exception $e) {
            $this->error("      Erreur: " . $e->getMessage());
            return false;
        }
    }
    
    private function testEmailTemplate(): bool
    {
        try {
            // Vérifier que le template existe
            $templatePath = resource_path('views/mail/reminder-candidate.blade.php');
            return file_exists($templatePath);
        } catch (\Exception $e) {
            $this->error("      Erreur: " . $e->getMessage());
            return false;
        }
    }
}
