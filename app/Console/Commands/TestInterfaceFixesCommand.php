<?php

namespace App\Console\Commands;

use App\Models\ReminderCandidate;
use App\Models\User;
use App\Models\Role;
use Illuminate\Console\Command;

class TestInterfaceFixesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-interface-fixes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Teste les corrections apportées à l\'interface des rappels candidats';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 TEST DES CORRECTIONS D\'INTERFACE');
        $this->info('=====================================');
        
        // Vérifier les données
        $this->checkData();
        
        // Instructions de test
        $this->displayTestInstructions();
        
        return 0;
    }
    
    private function checkData()
    {
        $this->info('');
        $this->info('📊 Vérification des données:');
        
        $reminders = ReminderCandidate::with('user')->get();
        $this->info("   - Rappels en base: {$reminders->count()}");
        
        if ($reminders->count() === 0) {
            $this->warn('   ⚠️ Aucun rappel trouvé. Créons des données de test...');
            $this->createTestData();
        } else {
            $this->info('   ✅ Données disponibles pour les tests');
        }
    }
    
    private function createTestData()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        if (!$candidateRole) {
            $this->error('   ❌ Rôle candidat non trouvé');
            return;
        }
        
        $users = User::where('role_id', $candidateRole->id)->take(3)->get();
        if ($users->count() === 0) {
            $this->warn('   ⚠️ Aucun utilisateur candidat trouvé');
            return;
        }
        
        foreach ($users as $user) {
            $existing = ReminderCandidate::where('user_id', $user->id)->first();
            if (!$existing) {
                ReminderCandidate::create([
                    'user_id' => $user->id,
                    'search_work' => null,
                    'last_reminder_sent_at' => now(),
                    'reminder_count' => 1,
                    'response_status' => 'no_response'
                ]);
                $this->info("   ✅ Reminder créé pour: {$user->firstname()} {$user->lastname()}");
            }
        }
    }
    
    private function displayTestInstructions()
    {
        $this->info('');
        $this->info('🧪 INSTRUCTIONS DE TEST:');
        $this->info('========================');
        
        $this->info('');
        $this->info('1. 🌐 Ouvrez la page des rappels candidats dans votre navigateur');
        $this->info('   URL: http://localhost:8000/admin/reminder-candidates');
        
        $this->info('');
        $this->info('2. 🔍 Ouvrez la console développeur (F12)');
        $this->info('   Vous devriez voir:');
        $this->info('   - "🚀 Initialisation du système de rappels candidats"');
        $this->info('   - "✅ Éléments trouvés, initialisation..."');
        $this->info('   - "✅ Interface initialisée avec succès"');
        
        $this->info('');
        $this->info('3. ✅ Testez les sélections:');
        $this->info('   a) Cliquez sur les checkboxes individuelles');
        $this->info('      → Le compteur doit se mettre à jour');
        $this->info('      → Le bouton "Envoyer email groupé" doit s\'activer/désactiver');
        $this->info('   b) Cliquez sur "Tout sélectionner"');
        $this->info('      → Toutes les checkboxes doivent se cocher/décocher');
        $this->info('      → Le texte du bouton doit changer');
        
        $this->info('');
        $this->info('4. 📧 Testez les emails:');
        $this->info('   a) Email individuel:');
        $this->info('      → Cliquez sur "Envoyer email" d\'une ligne');
        $this->info('      → Le modal doit s\'ouvrir');
        $this->info('   b) Email groupé:');
        $this->info('      → Sélectionnez des candidats');
        $this->info('      → Cliquez sur "Envoyer email groupé"');
        $this->info('      → Le modal doit s\'ouvrir');
        
        $this->info('');
        $this->info('5. 🔧 Utilisez le bouton "Debug JS" pour diagnostiquer les problèmes');
        
        $this->info('');
        $this->info('🚨 EN CAS DE PROBLÈME:');
        $this->info('- Vérifiez la console pour les erreurs JavaScript');
        $this->info('- Utilisez le bouton "Debug JS" pour voir l\'état du système');
        $this->info('- Vérifiez que les éléments HTML existent (checkboxes, boutons, modals)');
        
        $this->info('');
        $this->info('📋 CORRECTIONS APPORTÉES:');
        $this->info('- ✅ Code JavaScript simplifié et restructuré');
        $this->info('- ✅ Attente des éléments DOM avant initialisation');
        $this->info('- ✅ Logs de débogage détaillés');
        $this->info('- ✅ Gestionnaires d\'événements robustes');
        $this->info('- ✅ Vérifications d\'existence des éléments');
        
        $this->info('');
        $this->info('🎯 Si tout fonctionne, vous devriez pouvoir:');
        $this->info('- Sélectionner/désélectionner des candidats');
        $this->info('- Voir le compteur se mettre à jour en temps réel');
        $this->info('- Ouvrir les modals d\'email individuel et groupé');
        $this->info('- Utiliser le bouton "Tout sélectionner"');
    }
}
