<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ReminderCandidate;
use Illuminate\Support\Facades\Http;

class TestAjaxCallCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-ajax-call';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test de l\'appel AJAX pour la mise à jour du statut';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🌐 TEST DE L\'APPEL AJAX POUR UPDATE-STATUS');
        $this->info('==========================================');
        
        // Trouver un reminder pour tester
        $reminder = ReminderCandidate::with('user')->first();
        
        if (!$reminder) {
            $this->error('❌ Aucun reminder trouvé en base');
            return 1;
        }
        
        // Forcer un compteur > 0 pour le test
        $reminder->update([
            'no_response_reminder_count' => 3,
            'response_status' => 'no_response'
        ]);
        $reminder->refresh();
        
        $this->info("📊 Test avec le candidat: {$reminder->user->email}");
        $this->info("- ID: {$reminder->id}");
        $this->info("- Compteur initial: {$reminder->no_response_reminder_count}");
        $this->info("- Statut initial: {$reminder->response_status}");
        
        $this->info('');
        $this->info('🧪 TEST 1: Simulation appel AJAX avec cURL');
        $this->info('==========================================');
        
        try {
            // Simuler l'appel AJAX avec HTTP client Laravel
            $response = Http::withHeaders([
                'X-Requested-With' => 'XMLHttpRequest',
                'Accept' => 'application/json',
            ])->post("http://localhost:8000/admin/reminder-candidates/{$reminder->id}/update-status", [
                'response_status' => 'yes',
                '_token' => csrf_token()
            ]);
            
            $this->info("Status HTTP: {$response->status()}");
            $this->info("Réponse: " . $response->body());
            
            if ($response->successful()) {
                $data = $response->json();
                $this->info('✅ Appel AJAX réussi');
                $this->info("- success: " . ($data['success'] ? 'true' : 'false'));
                $this->info("- message: {$data['message']}");
                $this->info("- no_response_count: {$data['no_response_count']}");
                $this->info("- new_status: {$data['new_status']}");
                
                // Vérifier en base
                $reminder->refresh();
                $this->info('');
                $this->info('🔍 Vérification en base après appel:');
                $this->info("- no_response_reminder_count: {$reminder->no_response_reminder_count}");
                $this->info("- response_status: {$reminder->response_status}");
                
                if ($reminder->no_response_reminder_count == 0) {
                    $this->info('✅ Compteur correctement remis à 0');
                } else {
                    $this->error("❌ Compteur non remis à 0. Valeur: {$reminder->no_response_reminder_count}");
                }
                
            } else {
                $this->error('❌ Appel AJAX échoué');
                $this->error("Status: {$response->status()}");
                $this->error("Body: {$response->body()}");
            }
            
        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de l\'appel AJAX: ' . $e->getMessage());
        }
        
        $this->info('');
        $this->info('🧪 TEST 2: Vérification route directe');
        $this->info('=====================================');
        
        // Remettre un compteur > 0
        $reminder->update([
            'no_response_reminder_count' => 2,
            'response_status' => 'no_response'
        ]);
        $reminder->refresh();
        
        $this->info("Compteur avant: {$reminder->no_response_reminder_count}");
        
        try {
            // Appel direct de la méthode du contrôleur
            $controller = new \App\Http\Controllers\ReminderCandidateController();
            $request = new \Illuminate\Http\Request();
            $request->merge(['response_status' => 'yes']);
            
            $response = $controller->updateResponseStatus($request, $reminder);
            $responseData = $response->getData(true);
            
            $this->info('✅ Appel direct contrôleur réussi');
            $this->info("- success: " . ($responseData['success'] ? 'true' : 'false'));
            $this->info("- no_response_count: {$responseData['no_response_count']}");
            
            // Vérifier en base
            $reminder->refresh();
            $this->info("Compteur après: {$reminder->no_response_reminder_count}");
            
            if ($reminder->no_response_reminder_count == 0) {
                $this->info('✅ Contrôleur fonctionne correctement');
            } else {
                $this->error("❌ Contrôleur ne fonctionne pas. Compteur: {$reminder->no_response_reminder_count}");
            }
            
        } catch (\Exception $e) {
            $this->error('❌ Erreur appel direct contrôleur: ' . $e->getMessage());
        }
        
        $this->info('');
        $this->info('🎯 RÉSUMÉ DES TESTS:');
        $this->info('===================');
        
        $this->info('');
        $this->info('🚀 PROCHAINES ÉTAPES:');
        $this->info('1. Si les tests passent → Problème côté JavaScript');
        $this->info('2. Si les tests échouent → Problème côté serveur');
        $this->info('3. Testez l\'interface: http://localhost:8000/admin/reminder-candidates');
        $this->info('4. Ouvrez F12 → Console et Network pour voir les requêtes');
        
        $this->info('');
        $this->info('🔍 CORRECTIONS APPORTÉES:');
        $this->info('- JavaScript: FormData au lieu de JSON');
        $this->info('- Headers: Suppression Content-Type (auto avec FormData)');
        $this->info('- Token CSRF: _token au lieu de X-CSRF-TOKEN');
        
        return 0;
    }
}
