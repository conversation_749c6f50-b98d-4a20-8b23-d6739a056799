<?php

namespace App\Console\Commands;

use App\Models\ReminderCandidate;
use App\Models\User;
use App\Models\Role;
use Illuminate\Console\Command;

class TestReminderInterfaceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-reminder-interface';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Teste l\'interface des rappels candidats';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 TEST DE L\'INTERFACE DES RAPPELS CANDIDATS');
        $this->info('==============================================');
        
        // Test 1: Vérifier les données disponibles
        $this->testDataAvailability();
        
        // Test 2: Vérifier les routes
        $this->testRoutes();
        
        // Test 3: Simuler les actions de l'interface
        $this->testInterfaceActions();
        
        $this->info('');
        $this->info('✅ Tests terminés. Vérifiez la console du navigateur pour les logs JavaScript.');
    }
    
    private function testDataAvailability()
    {
        $this->info('');
        $this->info('📊 Test 1: Disponibilité des données');
        
        $reminders = ReminderCandidate::with('user')->get();
        $this->info("   - Nombre de rappels en base: {$reminders->count()}");
        
        if ($reminders->count() === 0) {
            $this->warn('   ⚠️ Aucun rappel trouvé. Créons des données de test...');
            $this->createTestData();
        }
        
        foreach ($reminders->take(3) as $reminder) {
            $userName = $reminder->user ? $reminder->user->firstname() : 'Utilisateur inconnu';
            $userEmail = $reminder->user ? $reminder->user->email : 'Email inconnu';
            $this->info("   - Rappel ID: {$reminder->id}, Utilisateur: {$userName} ({$userEmail})");
        }
    }
    
    private function testRoutes()
    {
        $this->info('');
        $this->info('🛣️ Test 2: Vérification des routes');
        
        try {
            $indexRoute = route('admin.reminder-candidates.index');
            $this->info("   ✅ Route index: {$indexRoute}");
            
            $testReminder = ReminderCandidate::first();
            if ($testReminder) {
                $sendEmailRoute = route('admin.reminder-candidates.send-email', $testReminder->id);
                $updateStatusRoute = route('admin.reminder-candidates.update-status', $testReminder->id);
                $this->info("   ✅ Route send-email: {$sendEmailRoute}");
                $this->info("   ✅ Route update-status: {$updateStatusRoute}");
            }
            
            $bulkEmailRoute = route('admin.reminder-candidates.send-bulk-emails');
            $testEmailRoute = route('admin.reminder-candidates.test-email');
            $this->info("   ✅ Route bulk-emails: {$bulkEmailRoute}");
            $this->info("   ✅ Route test-email: {$testEmailRoute}");
            
        } catch (\Exception $e) {
            $this->error("   ❌ Erreur de route: " . $e->getMessage());
        }
    }
    
    private function testInterfaceActions()
    {
        $this->info('');
        $this->info('🖱️ Test 3: Actions de l\'interface');
        
        $this->info('   📋 Pour tester l\'interface:');
        $this->info('   1. Ouvrez la page des rappels candidats dans votre navigateur');
        $this->info('   2. Ouvrez la console développeur (F12)');
        $this->info('   3. Vous devriez voir les logs de débogage:');
        $this->info('      - "🚀 Initialisation du système de rappels candidats"');
        $this->info('      - "📋 Éléments trouvés: {...}"');
        $this->info('   4. Testez les actions:');
        $this->info('      - Cliquez sur les checkboxes');
        $this->info('      - Cliquez sur "Tout sélectionner"');
        $this->info('      - Cliquez sur "Envoyer email" (bouton individuel)');
        $this->info('      - Cliquez sur "Envoyer email groupé" (après sélection)');
        $this->info('      - Cliquez sur "Tester Email"');
        
        $this->info('');
        $this->info('   🔍 Problèmes possibles à vérifier:');
        $this->info('   - Si "Éléments trouvés" montre des valeurs false ou 0');
        $this->info('   - Si les clics ne génèrent pas de logs dans la console');
        $this->info('   - Si les modals ne s\'ouvrent pas');
    }
    
    private function createTestData()
    {
        // Créer un utilisateur candidat de test si nécessaire
        $candidateRole = Role::where('slug', 'candidate')->first();
        if (!$candidateRole) {
            $this->error('   ❌ Rôle candidat non trouvé');
            return;
        }
        
        $testUser = User::where('role_id', $candidateRole->id)->first();
        if (!$testUser) {
            $this->warn('   ⚠️ Aucun utilisateur candidat trouvé');
            return;
        }
        
        // Créer un reminder de test
        $reminder = ReminderCandidate::create([
            'user_id' => $testUser->id,
            'search_work' => null,
            'last_reminder_sent_at' => now(),
            'reminder_count' => 1,
            'response_status' => 'no_response'
        ]);
        
        $this->info("   ✅ Reminder de test créé avec l'ID: {$reminder->id}");
    }
}
