<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ReminderCandidate;

class TestStatusResetCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-status-reset';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test de la remise à zéro du compteur lors du changement de statut';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 TEST DE LA REMISE À ZÉRO DU COMPTEUR');
        $this->info('=====================================');

        // Trouver un reminder avec un compteur > 0
        $reminder = ReminderCandidate::with('user')
            ->where('no_response_reminder_count', '>', 0)
            ->first();

        if (!$reminder) {
            $this->info('📊 Aucun reminder avec compteur > 0 trouvé. Création d\'un test...');

            // Créer un reminder de test
            $reminder = ReminderCandidate::with('user')->first();
            if (!$reminder) {
                $this->error('❌ Aucun reminder trouvé en base');
                return 1;
            }

            // Forcer un compteur > 0 pour le test
            $reminder->update([
                'no_response_reminder_count' => 3,
                'response_status' => 'no_response'
            ]);
            $reminder->refresh();
        }

        $this->info("📊 Test avec le candidat: {$reminder->user->email}");
        $this->info('');

        // Afficher l'état initial
        $this->info('🔍 ÉTAT INITIAL:');
        $this->info("- no_response_reminder_count: {$reminder->no_response_reminder_count}");
        $this->info("- response_status: {$reminder->response_status}");

        $this->info('');
        $this->info('🧪 TEST 1: Changement vers "yes"');
        $this->info('================================');

        try {
            // Tester markAsResponded avec "yes"
            $reminder->markAsResponded('yes');
            $reminder->refresh();

            $this->info('✅ markAsResponded("yes") exécuté');
            $this->info("- no_response_reminder_count: {$reminder->no_response_reminder_count}");
            $this->info("- response_status: {$reminder->response_status}");

            // Vérifications
            if ($reminder->no_response_reminder_count == 0) {
                $this->info('✅ Compteur correctement remis à 0');
            } else {
                $this->error("❌ Compteur non remis à 0. Valeur: {$reminder->no_response_reminder_count}");
            }

            if ($reminder->response_status === 'yes') {
                $this->info('✅ Statut correctement mis à "yes"');
            } else {
                $this->error("❌ Statut incorrect. Valeur: {$reminder->response_status}");
            }

        } catch (\Exception $e) {
            $this->error('❌ Erreur lors du test 1: ' . $e->getMessage());
            return 1;
        }

        $this->info('');
        $this->info('🎯 RÉSUMÉ DES TESTS:');
        $this->info('===================');
        $this->info('✅ markAsResponded("yes") fonctionne');
        $this->info('✅ Compteur remis à 0');

        $this->info('');
        $this->info('🚀 PROCHAINES ÉTAPES:');
        $this->info('1. Testez l\'interface web: http://localhost:8000/admin/reminder-candidates');
        $this->info('2. Trouvez un candidat avec "Nb rappels" > 0');
        $this->info('3. Changez son statut à "Oui" via le sélecteur');
        $this->info('4. Vérifiez que "Nb rappels" passe à 0');
        $this->info('5. Ouvrez F12 → Console pour voir les logs');

        return 0;
    }
}
