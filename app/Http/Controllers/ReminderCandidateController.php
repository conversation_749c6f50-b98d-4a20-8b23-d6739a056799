<?php

namespace App\Http\Controllers;

use App\Models\ReminderCandidate;
use App\Models\User;
use App\Models\Role;
use App\Mail\CustomCandidateMail;
use App\Mail\ReminderCandidateMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class ReminderCandidateController extends Controller
{
    /**
     * Afficher la liste des rappels candidats
     */
    public function index(Request $request)
    {
        $query = ReminderCandidate::with(['user', 'user.civility']);

        // Filtrage par statut de réponse
        if ($request->filled('response_status')) {
            $query->byResponseStatus($request->response_status);
        }

        // Filtrage par date de dernier rappel
        if ($request->filled('last_reminder_date')) {
            $date = $request->last_reminder_date;
            $query->whereDate('last_reminder_sent_at', $date);
        }

        // Recherche par nom/email
        if ($request->filled('search')) {
            $query->searchByCandidate($request->search);
        }

        // Gestion du tri
        $sortBy = $request->get('sort_by', 'last_reminder');
        $sortDirection = $request->get('sort_direction', 'asc');

        // Validation des paramètres de tri
        $allowedSorts = ['last_reminder', 'reminder_count', 'next_reminder'];
        $allowedDirections = ['asc', 'desc'];

        if (!in_array($sortBy, $allowedSorts)) {
            $sortBy = 'last_reminder';
        }

        if (!in_array($sortDirection, $allowedDirections)) {
            $sortDirection = 'asc';
        }

        // Appliquer le tri selon le type
        switch ($sortBy) {
            case 'last_reminder':
                $query->sortByLastReminder($sortDirection);
                break;
            case 'reminder_count':
                $query->sortByReminderCount($sortDirection);
                break;
            case 'next_reminder':
                $query->sortByNextReminder($sortDirection);
                break;
            default:
                $query->sortByLastReminder('asc');
        }

        $reminders = $query->paginate(15);

        // Statistiques
        $stats = $this->getStatistics();

        return view('admin.reminder-candidates.index', compact('reminders', 'stats', 'sortBy', 'sortDirection'));
    }

    /**
     * Envoyer un email individuel
     */
    public function sendIndividualEmail(Request $request, ReminderCandidate $reminder)
    {
        $request->validate([
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        try {
            Mail::to($reminder->user->email)->send(
                new CustomCandidateMail(
                    $reminder->user,
                    $request->subject,
                    $request->message
                )
            );

            // Incrémenter le compteur de rappels sans réponse
            $reminder->incrementNoResponseReminderCount();

            // Recharger le modèle pour avoir les valeurs à jour
            $reminder->refresh();

            return response()->json([
                'success' => true,
                'message' => 'Email envoyé avec succès à ' . $reminder->user->email,
                'no_response_count' => $reminder->no_response_reminder_count,
                'reminder_id' => $reminder->id
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'envoi de l\'email : ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Envoyer des emails groupés
     */
    public function sendBulkEmails(Request $request)
    {
        $request->validate([
            'reminder_ids' => 'required|array|min:1',
            'reminder_ids.*' => 'exists:reminder_candidates,_id',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        $reminders = ReminderCandidate::with('user')
            ->whereIn('_id', $request->reminder_ids)
            ->get();

        $successCount = 0;
        $errors = [];

        foreach ($reminders as $reminder) {
            try {
                Mail::to($reminder->user->email)->send(
                    new CustomCandidateMail(
                        $reminder->user,
                        $request->subject,
                        $request->message
                    )
                );

                // Incrémenter le compteur de rappels sans réponse
                $reminder->incrementNoResponseReminderCount();

                $successCount++;
            } catch (\Exception $e) {
                $errors[] = "Erreur pour {$reminder->user->email}: " . $e->getMessage();
            }
        }

        return response()->json([
            'success' => true,
            'message' => "{$successCount} emails envoyés avec succès",
            'errors' => $errors
        ]);
    }

    /**
     * Mettre à jour le statut de réponse d'un candidat
     */
    public function updateResponseStatus(Request $request, ReminderCandidate $reminder)
    {
        $request->validate([
            'response_status' => 'required|in:yes,no,no_response'
        ]);

        $status = $request->response_status;

        if ($status === 'yes' || $status === 'no') {
            // Le candidat a répondu, remettre le compteur de rappels sans réponse à zéro
            $reminder->markAsResponded($status);
            $reminder->refresh(); // Recharger pour avoir les valeurs à jour
        } else {
            // Statut "no_response" - garder le compteur actuel
            $reminder->update(['response_status' => $status]);
            $reminder->refresh(); // Recharger pour avoir les valeurs à jour
        }

        return response()->json([
            'success' => true,
            'message' => 'Statut mis à jour avec succès',
            'no_response_count' => $reminder->no_response_reminder_count,
            'new_status' => $reminder->response_status
        ]);
    }

    /**
     * Obtenir les statistiques des rappels
     */
    private function getStatistics()
    {
        $total = ReminderCandidate::count();

        // Pour MongoDB, nous comptons manuellement par statut
        $yesCount = ReminderCandidate::where('response_status', 'yes')->count();
        $noCount = ReminderCandidate::where('response_status', 'no')->count();
        $noResponseCount = ReminderCandidate::where('response_status', 'no_response')->count();

        // Calculer la moyenne des rappels
        $averageReminders = ReminderCandidate::avg('reminder_count') ?? 0;

        // Obtenir les plus anciens sans réponse
        $oldestWithoutResponse = ReminderCandidate::with('user')
            ->where('response_status', 'no_response')
            ->orderBy('last_reminder_sent_at', 'asc')
            ->limit(5)
            ->get();

        return [
            'total' => $total,
            'yes_count' => $yesCount,
            'no_count' => $noCount,
            'no_response_count' => $noResponseCount,
            'average_reminders' => round($averageReminders, 1),
            'oldest_without_response' => $oldestWithoutResponse
        ];
    }

    /**
     * Exporter les données en CSV
     */
    public function exportCsv(Request $request)
    {
        $query = ReminderCandidate::with(['user', 'user.civility']);

        // Appliquer les mêmes filtres que l'index
        if ($request->filled('response_status')) {
            $query->byResponseStatus($request->response_status);
        }

        if ($request->filled('last_reminder_date')) {
            $date = $request->last_reminder_date;
            $query->whereDate('last_reminder_sent_at', $date);
        }

        if ($request->filled('search')) {
            $query->searchByCandidate($request->search);
        }

        $reminders = $query->get();

        $filename = 'rappels_candidats_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($reminders) {
            $file = fopen('php://output', 'w');
            
            // En-têtes CSV
            fputcsv($file, [
                'Nom',
                'Prénom', 
                'Email',
                'Statut de réponse',
                'Date du dernier rappel',
                'Nombre de rappels',
                'Prochaine date de rappel'
            ]);

            // Données
            foreach ($reminders as $reminder) {
                $civility = $reminder->user->civility;
                fputcsv($file, [
                    $civility->last_name ?? '',
                    $civility->first_name ?? '',
                    $reminder->user->email,
                    $reminder->formatted_response_status,
                    $reminder->last_reminder_sent_at ? $reminder->last_reminder_sent_at->format('d/m/Y H:i') : '',
                    $reminder->reminder_count ?? 0,
                    $reminder->next_reminder_date ? $reminder->next_reminder_date->format('d/m/Y') : ''
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Tester l'envoi d'email (pour le débogage)
     */
    public function testEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        try {
            // Créer un reminder de test avec un ID valide
            $testReminder = new ReminderCandidate();
            $testReminder->id = 'test-' . uniqid();
            $testReminder->user_id = 'test-user-id';
            $testReminder->search_work = null;
            $testReminder->last_reminder_sent_at = now();
            $testReminder->reminder_count = 1;
            $testReminder->response_status = 'no_response';

            // Tenter l'envoi
            Mail::to($request->email)->send(new ReminderCandidateMail($testReminder));

            return response()->json([
                'success' => true,
                'message' => 'Email de test envoyé avec succès à ' . $request->email . '. Vérifiez votre boîte de réception.',
                'config' => [
                    'mailer' => Config::get('mail.default'),
                    'host' => Config::get('mail.mailers.smtp.host'),
                    'port' => Config::get('mail.mailers.smtp.port'),
                    'from' => Config::get('mail.from.address'),
                    'encryption' => Config::get('mail.mailers.smtp.encryption')
                ],
                'timestamp' => now()->format('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test email', [
                'email' => $request->email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'envoi : ' . $e->getMessage(),
                'config' => [
                    'mailer' => Config::get('mail.default'),
                    'host' => Config::get('mail.mailers.smtp.host'),
                    'port' => Config::get('mail.mailers.smtp.port'),
                    'from' => Config::get('mail.from.address')
                ]
            ], 500);
        }
    }
}
