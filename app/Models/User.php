<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Foundation\Auth\User as Authenticatable;
use App\Http\Controllers\Auth\UserVendor as Authenticatable;

use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Lara<PERSON>\Cashier\Billable;

use App\Notifications\CustomVerifyEmail;
use Illuminate\Support\Facades\URL;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, Billable;

    protected $connection = 'mongodb';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'is_suspend',
        'role_id',
        'old_id',
        'instance_of',
        'reset_token',
        'generated_password',
        'registered_at',
        'email_verified_at',
        'sended_email_update_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function getRoleSlugAttribute()
    {
        return $this->role()->slug;
    }

    public function role()
    {
        return Role::find($this->role_id);
    }


    // public function sendEmailVerificationNotification()
    // {
    //     $this->notify(new CustomVerifyEmail());
    // }
    public function sendEmailVerificationNotification()
    {
        // Extraire un nom par défaut depuis l'email
        $fullname = $this->getDefaultNameFromEmail($this->email);

        // Générer l'URL signée avec la méthode Laravel
        $actionUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $this->id, 'hash' => sha1($this->email)]
        );

        $roleSlug = $this->getRoleSlugAttribute();

        // Envoyer la notification avec l'URL correcte
        $this->notify(new CustomVerifyEmail($fullname, $this->email, $actionUrl, $roleSlug));
    }


    // Méthode privée pour générer un nom depuis l'email
    private function getDefaultNameFromEmail($email)
    {
        // Extraire la partie avant le '@'
        return lcfirst(explode('@', $email)[0]); // Première lettre en majuscule
    }


    public function civilityRelation()
    {
        return $this->hasOne(Civility::class);
    }

    public function civility()
    {
        return $this->hasOne(Civility::class, 'user_id', 'id');
    }

    public function phones()
    {
        return $this->hasMany(Phone::class, 'user_id', 'id');
    }

    public function fullname()
    {
        $civilty = $this->civility;

        if ($this->getRoleSlugAttribute() == 'recruter') {
            return $civilty ? $civilty->company_name : ''; // Provide a default value if $civilty is null
        } else {
            return ($civilty->first_name ?? '') . ' ' . ($civilty->last_name ?? '');
        }
    }


    public function firstname()
    {
        $civilty = $this->civility;

        if ($this->getRoleSlugAttribute() == 'recruter') {
            return $civilty ? $civilty->company_name : ''; // Provide a default value if $civilty is null
        } else {
            return ($civilty->first_name ?? '');
        }
    }



    public function getAddress()
    {
        $address = Address::where('user_id', $this->id)->first();

        if (!$address) {
            $address = new Address();
            $address->name = "";
            $address->lat = 0.0;
            $address->log = 0.0;
        }

        return $address;
    }


    public function getCountryOfResidence()
    {
        $civility = $this->civility;

        if (!$civility || !$civility->country_of_residence_country_id) {
            return null;
        }

        $country_id = $civility->country_of_residence_country_id;
        $countryOfResidence = Country::where('_id', $country_id)->first();

        return $countryOfResidence;
    }

    public function getPhoneNumber()
    {
        $phoneNumber = Phone::where('user_id', $this->id)->first();

        return $phoneNumber;
    }

    public function getResidencePermit()
    {
        $civility = $this->civility;

        if (!$civility || !$civility->residence_permit_id) {
            return null;
        }

        $permis_id = $civility->residence_permit_id;
        $permis = Permit::where('_id', $permis_id)->first();

        return $permis;
    }

    public function getProffession()
    {
        $civility = $this->civility;

        if (!$civility || !$civility->type_profession_id) {
            return null;
        }

        $type_profession_id = $civility->type_profession_id;
        $profession = Profession::where('_id', $type_profession_id)->first();

        return $profession;
    }

    public function getResponsiblityCandidat()
    {
        $civility = $this->civility;

        if (!$civility || !$civility->responsibility_candidate_id) {
            return null;
        }

        $responsibility_candidate_id = $civility->responsibility_candidate_id;
        $responsibilitycandidate = ResponsibilityCandidate::where('_id', $responsibility_candidate_id)->first();

        return $responsibilitycandidate;
    }

    public function getNatifLanguage()
    {
        // Récupérer la civility actuelle
        $civility = $this->civility;

        // Vérifier si native_language est bien défini et est un tableau
        if (isset($civility->native_language) && is_array($civility->native_language)) {
            foreach ($civility->native_language as $language) {
                // Traitez chaque élément du tableau ici si nécessaire
                // Par exemple, on pourrait les afficher ou les stocker dans un nouveau tableau
                $nativeLanguages[] = $language;
            }

            $nativeLanguagesList = Language::whereIn('_id', $nativeLanguages)->first();

            // Retourner tous les éléments du tableau
            return $nativeLanguagesList;
            // return $nativeLanguages;
        }

        // Si native_language n'est pas défini ou n'est pas un tableau, on peut retourner un tableau vide
        return [];
    }

    public function getFluentteLanguage()
    {
        // Récupérer la civility actuelle
        $civility = $this->civility;

        // Vérifier si native_language est bien défini et est un tableau
        if (isset($civility->fluent_languages) && is_array($civility->fluent_languages)) {
            foreach ($civility->fluent_languages as $language) {
                // Traitez chaque élément du tableau ici si nécessaire
                // Par exemple, on pourrait les afficher ou les stocker dans un nouveau tableau
                $fluentLanguages[] = $language;
            }

            $fluentLanguagesList = Language::whereIn('_id', $fluentLanguages)->first();

            // Retourner tous les éléments du tableau
            return $fluentLanguagesList;
            // return $nativeLanguages;
        }

        // Si native_language n'est pas défini ou n'est pas un tableau, on peut retourner un tableau vide
        return [];
    }


    public function getIntermediateLanguages()
    {
        // Récupérer la civility actuelle
        $civility = $this->civility;

        // Vérifier si native_language est bien défini et est un tableau
        if (isset($civility->intermediate_languages) && is_array($civility->intermediate_languages)) {
            foreach ($civility->intermediate_languages as $language) {
                // Traitez chaque élément du tableau ici si nécessaire
                // Par exemple, on pourrait les afficher ou les stocker dans un nouveau tableau
                $intermediateLanguages[] = $language;
            }

            $intermediateLanguagesList = Language::whereIn('_id', $intermediateLanguages)->first();

            // Retourner tous les éléments du tableau
            return $intermediateLanguagesList;
            // return $nativeLanguages;
        }

        // Si native_language n'est pas défini ou n'est pas un tableau, on peut retourner un tableau vide
        return [];
    }

    public function getBasicLanguages()
    {
        // Récupérer la civility actuelle
        $civility = $this->civility;

        // Vérifier si native_language est bien défini et est un tableau
        if (isset($civility->basic_languages) && is_array($civility->basic_languages)) {
            foreach ($civility->basic_languages as $language) {
                // Traitez chaque élément du tableau ici si nécessaire
                // Par exemple, on pourrait les afficher ou les stocker dans un nouveau tableau
                $basicLanguagesLanguages[] = $language;
            }

            $basicLanguagesList = Language::whereIn('_id', $basicLanguagesLanguages)->first();

            // Retourner tous les éléments du tableau
            return $basicLanguagesList;
            // return $nativeLanguages;
        }

        // Si native_language n'est pas défini ou n'est pas un tableau, on peut retourner un tableau vide
        return [];
    }



    public function userProfessions()
    {
        return $this->hasMany(UserProfession::class);
    }

    public function userFieldActivities()
    {
        return $this->hasMany(UserFieldActivity::class);
    }

    public function userPermits()
    {
        return $this->hasMany(UserPermit::class);
    }

    public function profilePhoto()
    {
        return $this->hasOne(\App\Models\File::class, 'user_id', '_id')->where('usage', 'profile_picture');
    }

    public function address()
    {
        return $this->hasOne(Address::class); // ou la relation appropriée
    }

    /**
     * Relation avec les rappels candidats
     */
    public function reminderCandidate()
    {
        return $this->hasOne(ReminderCandidate::class, 'user_id');
    }
}
