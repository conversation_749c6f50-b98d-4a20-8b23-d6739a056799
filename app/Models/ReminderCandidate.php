<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use MongoDB\Laravel\Eloquent\Model;
use Carbon\Carbon;

class ReminderCandidate extends Model
{
    use HasFactory;

    protected $connection = 'mongodb';

    protected $table = 'reminder_candidates';

    protected $fillable = [
        'user_id',
        'search_work',
        'last_reminder_sent_at',
        'reminder_count',
        'no_response_reminder_count',
        'response_status'
    ];

    protected $casts = [
        'search_work' => 'boolean',
        'last_reminder_sent_at' => 'datetime',
        'reminder_count' => 'integer',
        'no_response_reminder_count' => 'integer',
    ];

    protected $attributes = [
        'reminder_count' => 0,
        'no_response_reminder_count' => 0,
        'response_status' => 'no_response',
    ];

    // Constantes pour les statuts de réponse
    const RESPONSE_STATUS_YES = 'yes';
    const RESPONSE_STATUS_NO = 'no';
    const RESPONSE_STATUS_NO_RESPONSE = 'no_response';

    /**
     * Relation avec le modèle User
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Calculer la prochaine date de rappel
     */
    public function getNextReminderDateAttribute()
    {
        if (!$this->last_reminder_sent_at) {
            return null;
        }

        // Logique de rappel : tous les 15 jours
        return $this->last_reminder_sent_at->addDays(15);
    }

    /**
     * Vérifier si un rappel est dû
     */
    public function isReminderDue()
    {
        if (!$this->last_reminder_sent_at) {
            return false;
        }

        return now()->greaterThanOrEqualTo($this->next_reminder_date);
    }

    /**
     * Obtenir le statut de réponse formaté
     */
    public function getFormattedResponseStatusAttribute()
    {
        switch ($this->response_status) {
            case self::RESPONSE_STATUS_YES:
                return 'Oui';
            case self::RESPONSE_STATUS_NO:
                return 'Non';
            case self::RESPONSE_STATUS_NO_RESPONSE:
            default:
                return 'Pas de réponse';
        }
    }

    /**
     * Obtenir la couleur du badge selon le statut
     */
    public function getStatusBadgeColorAttribute()
    {
        switch ($this->response_status) {
            case self::RESPONSE_STATUS_YES:
                return 'bg-green-100 text-green-800';
            case self::RESPONSE_STATUS_NO:
                return 'bg-red-100 text-red-800';
            case self::RESPONSE_STATUS_NO_RESPONSE:
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    /**
     * Incrémenter le compteur de rappels
     */
    public function incrementReminderCount()
    {
        $this->increment('reminder_count');
        $this->update(['last_reminder_sent_at' => now()]);
    }

    /**
     * Incrémenter le compteur de rappels sans réponse
     * Cette méthode est appelée à chaque envoi de rappel
     */
    public function incrementNoResponseReminderCount()
    {
        // Incrémenter les compteurs et mettre à jour en une seule opération
        $this->update([
            'reminder_count' => ($this->reminder_count ?? 0) + 1,
            'no_response_reminder_count' => ($this->no_response_reminder_count ?? 0) + 1,
            'last_reminder_sent_at' => now(),
            'response_status' => 'no_response'
        ]);

        // Recharger le modèle pour avoir les valeurs à jour
        $this->refresh();
    }

    /**
     * Marquer qu'une réponse a été donnée
     * Remet le compteur de rappels sans réponse à zéro
     * Réactive les rappels si le statut est "yes", les désactive si le statut est "no"
     */
    public function markAsResponded($status)
    {
        $updateData = [
            'response_status' => $status,
            'no_response_reminder_count' => 0
        ];

        if ($status === 'yes') {
            // Candidat toujours en recherche d'emploi - réactiver les rappels
            $updateData['search_work'] = true;
            $updateData['last_reminder_sent_at'] = now();

            // Réactiver aussi la visibilité du profil
            $user = \App\Models\User::find($this->user_id);
            if ($user) {
                $civility = \App\Models\Civility::where('user_id', $user->id)->first();
                if ($civility) {
                    $civility->visibility = '1';
                    $civility->save();
                }
            }
        } elseif ($status === 'no') {
            // Candidat a trouvé un emploi - désactiver les rappels
            $updateData['search_work'] = false;
            $updateData['last_reminder_sent_at'] = now(); // Pour historique

            // Masquer aussi la visibilité du profil
            $user = \App\Models\User::find($this->user_id);
            if ($user) {
                $civility = \App\Models\Civility::where('user_id', $user->id)->first();
                if ($civility) {
                    $civility->visibility = '0';
                    $civility->save();
                }
            }
        }

        $this->update($updateData);
    }

    /**
     * Scope pour filtrer par statut de réponse
     */
    public function scopeByResponseStatus($query, $status)
    {
        return $query->where('response_status', $status);
    }

    /**
     * Scope pour les rappels en retard
     */
    public function scopeOverdue($query)
    {
        return $query->where('last_reminder_sent_at', '<=', now()->subDays(15));
    }

    /**
     * Scope pour recherche par nom/email du candidat
     */
    public function scopeSearchByCandidate($query, $search)
    {
        return $query->whereHas('user', function ($q) use ($search) {
            $q->where('email', 'like', "%{$search}%")
              ->orWhereHas('civilityRelation', function ($subQuery) use ($search) {
                  $subQuery->where('first_name', 'like', "%{$search}%")
                           ->orWhere('last_name', 'like', "%{$search}%");
              });
        });
    }

    /**
     * Scope pour trier par statut de réponse
     */
    public function scopeSortByStatus($query, $direction = 'asc')
    {
        return $query->orderBy('response_status', $direction);
    }

    /**
     * Scope pour trier par date du dernier rappel
     */
    public function scopeSortByLastReminder($query, $direction = 'asc')
    {
        return $query->orderBy('last_reminder_sent_at', $direction);
    }

    /**
     * Scope pour trier par nombre de rappels
     */
    public function scopeSortByReminderCount($query, $direction = 'asc')
    {
        return $query->orderBy('no_response_reminder_count', $direction);
    }

    /**
     * Scope pour trier par prochaine date de rappel
     * Note: Comme next_reminder_date est un attribut calculé, nous trions par last_reminder_sent_at
     */
    public function scopeSortByNextReminder($query, $direction = 'asc')
    {
        return $query->orderBy('last_reminder_sent_at', $direction);
    }
}
