# Logique des Rappels Candidats

## Vue d'ensemble

Le système de rappels candidats envoie périodiquement des emails aux candidats pour vérifier s'ils sont toujours à la recherche d'un emploi. Cette documentation décrit la nouvelle logique implémentée.

## Nouvelle Logique

### 1. Premier rappel (candidats sans reminder)
- **D<PERSON>lai** : 12 jours après l'inscription
- **Action** : Création d'un `ReminderCandidate` et envoi du premier email
- **État** : `search_work = null`, `last_reminder_sent_at = now()`

### 2. Candidats avec reminder existant

#### Cas 1 : Candidat a refusé (`search_work = false`)
- **Action** : Aucun rappel supplémentaire
- **Profil** : <PERSON><PERSON><PERSON><PERSON> (`visibility = '0'`)
- **Statut** : Arrêt définitif des rappels

#### Cas 2 : Candidat n'a pas répondu (`search_work = null`)
- **Action** : Rappel tous les 14 jours depuis le dernier envoi
- **Profil** : Masqué (`visibility = '0'`)
- **Statut** : Rappels continus jusqu'à réponse

#### Cas 3 : Candidat a accepté (`search_work = true`)
- **Action** : Rappel tous les 14 jours depuis le dernier envoi
- **Profil** : Reste visible (`visibility = '1'`)
- **Statut** : Rappels continus tous les 14 jours

## Réponses des candidats

### Réponse "Oui" (toujours à la recherche)
- `search_work = true`
- `visibility = '1'` (profil visible)
- `last_reminder_sent_at = now()` (reset du cycle)
- `no_response_reminder_count = 0` (remise à zéro du compteur)
- Prochain rappel dans 14 jours

### Réponse "Non" (plus à la recherche)
- `search_work = false`
- `visibility = '0'` (profil masqué)
- `last_reminder_sent_at = now()` (pour historique)
- `no_response_reminder_count = 0` (remise à zéro du compteur)
- Arrêt définitif des rappels

## Timeline exemple

```
Jour 0   : Inscription candidat
Jour 12  : Premier rappel
Jour 26  : Deuxième rappel (si pas de réponse ou "Oui")
Jour 40  : Troisième rappel (si pas de réponse ou "Oui")
...      : Cycle continue tous les 14 jours
```

## Champs de la table `reminder_candidates`

- `user_id` : ID du candidat
- `search_work` : 
  - `null` = pas de réponse
  - `true` = toujours à la recherche
  - `false` = plus à la recherche (arrêt rappels)
- `last_reminder_sent_at` : Date du dernier rappel envoyé
- `created_at` : Date de création du reminder
- `updated_at` : Date de dernière modification

## Commande Artisan

```bash
php artisan app:reminder-candidate
```

Cette commande doit être programmée quotidiennement via le scheduler Laravel.

## Logs et monitoring

La commande affiche des informations sur :
- Nombre de candidats traités
- Rappels envoyés avec succès
- Erreurs d'envoi d'emails
- Candidats qui ont refusé (pas de rappel)
