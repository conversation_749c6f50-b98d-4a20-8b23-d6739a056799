# Réactivation des Rappels Candidats - Documentation

## Vue d'ensemble

Cette documentation décrit les modifications apportées pour réactiver automatiquement les notifications de rappels candidats dans deux scénarios spécifiques :
1. Quand le candidat clique sur "Afficher mon profil" dans son espace
2. Quand l'admin change le statut à "Oui" via les actions

## Problème identifié

Avant ces modifications, les rappels n'étaient pas gérés automatiquement selon la visibilité du profil :
- Un candidat décidait de rendre son profil visible après l'avoir masqué → Rappels non réactivés
- Un candidat décidait de masquer son profil après l'avoir affiché → Rappels non désactivés
- Un admin changeait manuellement le statut d'un candidat → Visibilité non synchronisée

Cela créait des incohérences entre la visibilité du profil et l'état des rappels.

## Correction importante

### Statut lors de l'affichage de profil

**Problème initial :** La première implémentation mettait le statut à `'no_response'` quand un candidat affichait son profil.

**Correction appliquée :** Le statut est maintenant mis à `'yes'` car afficher son profil signifie être en recherche d'emploi.

**Justification :**
- Logique métier : Afficher son profil = Je cherche encore du travail
- Cohérence : Même statut pour toutes les actions positives
- Statistiques : Comptabilisation correcte dans "Toujours en recherche d'emploi"

## Solutions implémentées

### 1. Gestion complète de la visibilité du profil candidat

#### Modification dans `CandidateController.php`

**Méthode `profileUpdate()` modifiée :**
```php
// Vérifier si la visibilité change
$oldVisibility = $civility->visibility;
$newVisibility = $request->visibility;

$civility->visibility = $newVisibility;
$civility->save();

// Gérer les changements de visibilité
if ($oldVisibility != $newVisibility) {
    if ($oldVisibility == '0' && $newVisibility == '1') {
        // Candidat réactive son profil (masqué → affiché) = cherche encore du travail
        $this->reactivateReminders($user->id);
    } elseif ($oldVisibility == '1' && $newVisibility == '0') {
        // Candidat masque son profil (affiché → masqué) = ne cherche plus de travail
        $this->deactivateReminders($user->id);
    }
}
```

**Nouvelle méthode `reactivateReminders()` :**
```php
private function reactivateReminders($userId)
{
    // Trouver ou créer un reminder_candidate pour cet utilisateur
    $reminderCandidate = ReminderCandidate::where('user_id', $userId)->first();
    
    if (!$reminderCandidate) {
        // Créer un nouveau reminder_candidate si il n'existe pas
        $reminderCandidate = new ReminderCandidate();
        $reminderCandidate->user_id = $userId;
        $reminderCandidate->response_status = 'yes';
        $reminderCandidate->no_response_reminder_count = 0;
        $reminderCandidate->reminder_count = 0;
        $reminderCandidate->search_work = true;
        $reminderCandidate->last_reminder_sent_at = now();
        $reminderCandidate->save();
    } else {
        // Réactiver les rappels pour un reminder existant
        // Utiliser markAsResponded pour avoir la même logique que les autres réponses "oui"
        $reminderCandidate->markAsResponded('yes');
        $reminderCandidate->save();
    }
    
    Log::info("Rappels réactivés pour l'utilisateur {$userId} suite à l'affichage du profil");
}
```

**Nouvelle méthode `deactivateReminders()` :**
```php
private function deactivateReminders($userId)
{
    // Trouver le reminder_candidate pour cet utilisateur
    $reminderCandidate = ReminderCandidate::where('user_id', $userId)->first();

    if ($reminderCandidate) {
        // Utiliser markAsResponded pour appliquer toute la logique du statut "no"
        $reminderCandidate->markAsResponded('no');
        Log::info("Rappels désactivés pour l'utilisateur {$userId} suite au masquage du profil - statut: no");
    } else {
        // Créer un nouveau reminder_candidate avec statut "no" si il n'existe pas
        $reminderCandidate = new ReminderCandidate();
        $reminderCandidate->user_id = $userId;
        $reminderCandidate->response_status = 'no';
        $reminderCandidate->no_response_reminder_count = 0;
        $reminderCandidate->reminder_count = 0;
        $reminderCandidate->search_work = false;
        $reminderCandidate->last_reminder_sent_at = now();
        $reminderCandidate->save();
        Log::info("Nouveau reminder créé avec statut 'no' pour l'utilisateur {$userId} suite au masquage du profil");
    }
}
```

### 2. Réactivation via changement de statut admin

#### Modification dans `ReminderCandidate.php`

**Méthode `markAsResponded()` complètement étendue :**
```php
public function markAsResponded($status)
{
    $updateData = [
        'response_status' => $status,
        'no_response_reminder_count' => 0
    ];

    if ($status === 'yes') {
        // Candidat toujours en recherche d'emploi - réactiver les rappels
        $updateData['search_work'] = true;
        $updateData['last_reminder_sent_at'] = now();

        // Réactiver aussi la visibilité du profil
        $user = \App\Models\User::find($this->user_id);
        if ($user) {
            $civility = \App\Models\Civility::where('user_id', $user->id)->first();
            if ($civility) {
                $civility->visibility = '1';
                $civility->save();
            }
        }
    } elseif ($status === 'no') {
        // Candidat a trouvé un emploi - désactiver les rappels
        $updateData['search_work'] = false;
        $updateData['last_reminder_sent_at'] = now(); // Pour historique

        // Masquer aussi la visibilité du profil
        $user = \App\Models\User::find($this->user_id);
        if ($user) {
            $civility = \App\Models\Civility::where('user_id', $user->id)->first();
            if ($civility) {
                $civility->visibility = '0';
                $civility->save();
            }
        }
    }

    $this->update($updateData);
}
```

## Scénarios d'utilisation

### Scénario 1 : Candidat réactive son profil

1. **État initial :**
   - Candidat avec profil masqué (`visibility = '0'`)
   - Rappels inactifs (`search_work = false`)
   - Statut quelconque

2. **Action :**
   - Candidat va dans son espace personnel
   - Change "Afficher mon profil" de "Masquer" à "Afficher"
   - Sauvegarde son profil

3. **Résultat automatique :**
   - `visibility = '1'` ✅
   - `search_work = true` ✅
   - `response_status = 'yes'` ✅ (corrigé : afficher profil = cherche travail)
   - `last_reminder_sent_at = now()` ✅
   - `no_response_reminder_count = 0` ✅ (remis à zéro car c'est une réponse positive)
   - Rappels réactivés pour les prochains cycles

### Scénario 2 : Candidat masque son profil

1. **État initial :**
   - Candidat avec profil affiché (`visibility = '1'`)
   - Rappels actifs (`search_work = true`)
   - Statut "Oui" ou autre

2. **Action :**
   - Candidat va dans son espace personnel
   - Change "Afficher mon profil" de "Afficher" à "Masquer"
   - Sauvegarde son profil

3. **Résultat automatique :**
   - `visibility = '0'` ✅
   - `search_work = false` ✅
   - `response_status = 'no'` ✅ (masquer profil = ne cherche plus de travail)
   - `last_reminder_sent_at = now()` ✅ (pour historique)
   - `no_response_reminder_count = 0` ✅ (remis à zéro car c'est une réponse négative)
   - Rappels désactivés définitivement

### Scénario 3 : Admin change le statut à "Oui"

1. **État initial :**
   - Candidat avec statut "Non" ou "Pas de réponse"
   - Profil potentiellement masqué
   - Rappels potentiellement inactifs

2. **Action :**
   - Admin va dans la gestion des rappels candidats
   - Utilise le bouton "Actions" sur un candidat
   - Change le statut à "Oui" (toujours en recherche d'emploi)

3. **Résultat automatique :**
   - `response_status = 'yes'` ✅
   - `search_work = true` ✅
   - `visibility = '1'` ✅
   - `last_reminder_sent_at = now()` ✅
   - `no_response_reminder_count = 0` ✅ (remis à zéro car c'est une réponse)
   - Rappels réactivés pour les prochains cycles

### Scénario 4 : Candidat clique "Oui" dans email

1. **Action :**
   - Candidat clique sur "Oui" dans l'email de rappel

2. **Résultat automatique :**
   - Même logique que le scénario 2 (utilise `markAsResponded('yes')`)
   - Rappels réactivés + profil rendu visible

## Avantages des modifications

### 1. Synchronisation bidirectionnelle Admin ↔ Candidat
- **Admin → Candidat :** Changement de statut via admin se reflète automatiquement dans l'espace candidat
- **Candidat → Admin :** Changement de visibilité par le candidat met à jour automatiquement le statut admin
- **Cohérence garantie :** Impossible d'avoir des états incohérents entre les deux interfaces
- **Temps réel :** Synchronisation immédiate sans délai

### 2. Réactivation intelligente
- **Détection automatique :** Le système détecte quand un candidat souhaite redevenir actif
- **Action proactive :** Réactivation immédiate sans intervention manuelle
- **Cohérence :** Même logique que ce soit via candidat ou admin

### 3. Expérience utilisateur améliorée
- **Candidats :** Peuvent facilement redevenir actifs en affichant leur profil
- **Admins :** Peuvent réactiver les rappels en changeant le statut
- **Automatisation :** Moins d'interventions manuelles nécessaires

### 4. Fiabilité des données
- **Cohérence :** Visibilité et rappels synchronisés
- **Traçabilité :** Logs des réactivations
- **Logique métier :** Respecte les règles business

## Synchronisation bidirectionnelle

### Admin → Candidat
Quand l'admin change le statut via le bouton "Actions" :

| Action Admin | Statut Rappel | Espace Candidat | Automatique |
|-------------|---------------|-----------------|-------------|
| Met statut "Oui" | `response_status = 'yes'` | "Afficher mon profil" = **Afficher** | ✅ |
| Met statut "Non" | `response_status = 'no'` | "Afficher mon profil" = **Masquer** | ✅ |
| Met statut "Pas de réponse" | `response_status = 'no_response'` | Aucun changement | ✅ |

### Candidat → Admin
Quand le candidat change la visibilité dans son espace :

| Action Candidat | Visibilité | Statut Rappel | Automatique |
|----------------|------------|---------------|-------------|
| Sélectionne "Afficher" | `visibility = '1'` | `response_status = 'yes'` | ✅ |
| Sélectionne "Masquer" | `visibility = '0'` | `response_status = 'no'` | ✅ |

### Règles de cohérence
- **Règle 1 :** `response_status = 'yes'` ⟺ `visibility = '1'`
- **Règle 2 :** `response_status = 'no'` ⟺ `visibility = '0'`
- **Règle 3 :** `response_status = 'no_response'` → `visibility` inchangé

## Comportement détaillé

### Réactivation via profil candidat (affichage)
- ✅ Détecte le changement `visibility: '0' → '1'`
- ✅ Réactive `search_work = true`
- ✅ Met à jour `last_reminder_sent_at`
- ✅ Change `response_status = 'yes'` ✅ (afficher profil = réponse positive)
- ✅ **Remet à zéro** le compteur (c'est une réponse positive)
- ✅ Force `visibility = '1'`
- ✅ Log de l'action

### Désactivation via profil candidat (masquage)
- ✅ Détecte le changement `visibility: '1' → '0'`
- ✅ Désactive `search_work = false`
- ✅ Met à jour `last_reminder_sent_at` (historique)
- ✅ Change `response_status = 'no'` ✅ (masquer profil = réponse négative)
- ✅ **Remet à zéro** le compteur (c'est une réponse négative)
- ✅ Force `visibility = '0'`
- ✅ Log de l'action

### Réactivation via admin "Oui"
- ✅ Réactive `search_work = true`
- ✅ Met à jour `last_reminder_sent_at`
- ✅ Change `response_status = 'yes'`
- ✅ Force `visibility = '1'`
- ✅ **Remet à zéro** le compteur (c'est une réponse)

### Désactivation via admin "Non"
- ✅ Réactive `search_work = false`
- ✅ Met à jour `last_reminder_sent_at` (historique)
- ✅ Change `response_status = 'no'`
- ✅ Force `visibility = '0'`
- ✅ **Remet à zéro** le compteur (c'est une réponse)

### Pas de changement
- ❌ Changement `visibility: '1' → '1'` (pas de changement)
- ❌ Changement `visibility: '0' → '0'` (pas de changement)
- ❌ Statut admin "Pas de réponse" (garde l'état actuel)

## Tests et validation

### Tests automatisés
- ✅ Vérification de la logique `markAsResponded('yes')`
- ✅ Test de la méthode `reactivateReminders()`
- ✅ Validation des différents scénarios
- ✅ Contrôle de la cohérence des données

### Tests manuels recommandés
1. **Test candidat :** Se connecter comme candidat, masquer puis afficher le profil
2. **Test admin :** Changer le statut d'un candidat à "Oui" via l'interface admin
3. **Test email :** Cliquer sur "Oui" dans un email de rappel
4. **Vérification :** Contrôler que les rappels sont bien envoyés dans les cycles suivants

## Compatibilité

- ✅ **Rétrocompatible :** Aucun impact sur les données existantes
- ✅ **Logique préservée :** Les comportements existants sont maintenus
- ✅ **Performance :** Ajout minimal de traitement
- ✅ **Sécurité :** Aucune faille introduite

## Conclusion

Ces modifications permettent une réactivation intelligente et automatique des rappels candidats, améliorant significativement l'expérience utilisateur et la fiabilité du système. Les candidats peuvent maintenant facilement redevenir actifs, et les admins disposent d'outils cohérents pour gérer les statuts.
