<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Interface Rappels</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Test Interface Rappels Candidats</h1>
        
        <!-- Contrôles -->
        <div class="bg-white p-4 rounded-lg shadow mb-6">
            <div class="flex items-center space-x-4">
                <button id="select-all-btn" 
                        class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                    Tout sélectionner
                </button>
                <button id="bulk-email-btn" 
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed" 
                        disabled>
                    Envoyer email groupé
                </button>
                <span id="selected-count" class="text-sm text-gray-600">0 candidat(s) sélectionné(s)</span>
                <button id="debug-btn" class="px-4 py-2 bg-purple-600 text-white rounded-md">Debug</button>
            </div>
        </div>
        
        <!-- Tableau de test -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            <input type="checkbox" id="select-all-checkbox" class="rounded">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Candidat</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr>
                        <td class="px-6 py-4">
                            <input type="checkbox" class="reminder-checkbox rounded" value="1">
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-gray-900">Jean Dupont</div>
                            <div class="text-sm text-gray-500"><EMAIL></div>
                        </td>
                        <td class="px-6 py-4">
                            <button class="send-individual-email text-blue-600 hover:text-blue-900"
                                    data-reminder-id="1"
                                    data-candidate-name="Jean Dupont"
                                    data-candidate-email="<EMAIL>">
                                Envoyer email
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4">
                            <input type="checkbox" class="reminder-checkbox rounded" value="2">
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-gray-900">Marie Martin</div>
                            <div class="text-sm text-gray-500"><EMAIL></div>
                        </td>
                        <td class="px-6 py-4">
                            <button class="send-individual-email text-blue-600 hover:text-blue-900"
                                    data-reminder-id="2"
                                    data-candidate-name="Marie Martin"
                                    data-candidate-email="<EMAIL>">
                                Envoyer email
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4">
                            <input type="checkbox" class="reminder-checkbox rounded" value="3">
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-gray-900">Pierre Durand</div>
                            <div class="text-sm text-gray-500"><EMAIL></div>
                        </td>
                        <td class="px-6 py-4">
                            <button class="send-individual-email text-blue-600 hover:text-blue-900"
                                    data-reminder-id="3"
                                    data-candidate-name="Pierre Durand"
                                    data-candidate-email="<EMAIL>">
                                Envoyer email
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Modal de test -->
        <div id="individual-email-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
            <div class="relative top-20 mx-auto p-5 border w-1/2 shadow-lg rounded-md bg-white">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Modal de test</h3>
                <p id="modal-content">Contenu du modal</p>
                <button id="close-modal" class="mt-4 px-4 py-2 bg-gray-500 text-white rounded">Fermer</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Test d\'interface démarré');
            
            const selectedReminders = new Set();
            const selectAllCheckbox = document.getElementById('select-all-checkbox');
            const reminderCheckboxes = document.querySelectorAll('.reminder-checkbox');
            const selectedCountSpan = document.getElementById('selected-count');
            const bulkEmailBtn = document.getElementById('bulk-email-btn');
            const selectAllBtn = document.getElementById('select-all-btn');
            const debugBtn = document.getElementById('debug-btn');
            const modal = document.getElementById('individual-email-modal');
            const closeModalBtn = document.getElementById('close-modal');
            const modalContent = document.getElementById('modal-content');
            
            console.log('📋 Éléments trouvés:', {
                selectAllCheckbox: !!selectAllCheckbox,
                reminderCheckboxes: reminderCheckboxes.length,
                selectedCountSpan: !!selectedCountSpan,
                bulkEmailBtn: !!bulkEmailBtn,
                selectAllBtn: !!selectAllBtn,
                debugBtn: !!debugBtn,
                modal: !!modal
            });
            
            function updateSelectedCount() {
                const checkedBoxes = document.querySelectorAll('.reminder-checkbox:checked');
                const count = checkedBoxes.length;
                
                selectedReminders.clear();
                checkedBoxes.forEach(cb => selectedReminders.add(cb.value));
                
                selectedCountSpan.textContent = `${count} candidat(s) sélectionné(s)`;
                bulkEmailBtn.disabled = count === 0;
                
                console.log('📊 Mise à jour:', count, 'sélectionnés');
            }
            
            // Gestionnaires pour les checkboxes
            reminderCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    console.log('✅ Checkbox changée:', this.value, this.checked);
                    updateSelectedCount();
                });
            });
            
            // Gestionnaire pour "Tout sélectionner"
            if (selectAllBtn) {
                selectAllBtn.addEventListener('click', function() {
                    const allChecked = document.querySelectorAll('.reminder-checkbox:checked').length === reminderCheckboxes.length;
                    reminderCheckboxes.forEach(checkbox => {
                        checkbox.checked = !allChecked;
                    });
                    updateSelectedCount();
                    this.textContent = allChecked ? 'Tout sélectionner' : 'Tout désélectionner';
                    console.log('🔄 Tout sélectionner/désélectionner');
                });
            }
            
            // Gestionnaire pour les boutons d'email individuel
            document.querySelectorAll('.send-individual-email').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('📧 Clic email individuel:', this.dataset.candidateName);
                    
                    modalContent.textContent = `Email pour: ${this.dataset.candidateName} (${this.dataset.candidateEmail})`;
                    modal.classList.remove('hidden');
                });
            });
            
            // Gestionnaire pour email groupé
            if (bulkEmailBtn) {
                bulkEmailBtn.addEventListener('click', function() {
                    console.log('📧 Clic email groupé, sélectionnés:', selectedReminders.size);
                    if (selectedReminders.size > 0) {
                        modalContent.textContent = `Email groupé pour ${selectedReminders.size} candidats`;
                        modal.classList.remove('hidden');
                    }
                });
            }
            
            // Fermer modal
            if (closeModalBtn) {
                closeModalBtn.addEventListener('click', function() {
                    modal.classList.add('hidden');
                });
            }
            
            // Debug
            if (debugBtn) {
                debugBtn.addEventListener('click', function() {
                    console.log('🔍 DEBUG:', {
                        checkboxes: reminderCheckboxes.length,
                        checked: document.querySelectorAll('.reminder-checkbox:checked').length,
                        selected: selectedReminders.size,
                        bulkBtnDisabled: bulkEmailBtn.disabled
                    });
                });
            }
            
            // Initialisation
            updateSelectedCount();
            console.log('✅ Test d\'interface initialisé');
        });
    </script>
</body>
</html>
