<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Final - Interface Rappels</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">🎯 Test Final - Interface Rappels Candidats</h1>
        
        <!-- Status de test -->
        <div id="test-status" class="mb-6 p-4 bg-white rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">État du test</h2>
            <div id="status-messages" class="space-y-2">
                <div class="text-gray-600">⏳ Initialisation du test...</div>
            </div>
        </div>
        
        <!-- Simulation de l'interface -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Simulation de l'interface</h2>
            
            <!-- Contrôles -->
            <div class="mb-6 p-4 bg-gray-50 rounded">
                <div class="flex items-center space-x-4">
                    <button id="select-all-btn" 
                            class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                        Tout sélectionner
                    </button>
                    <button id="bulk-email-btn" 
                            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed" 
                            disabled>
                        Envoyer email groupé
                    </button>
                    <span id="selected-count" class="text-sm text-gray-600">0 candidat(s) sélectionné(s)</span>
                </div>
            </div>
            
            <!-- Tableau de test -->
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left">
                            <input type="checkbox" id="select-all-checkbox" class="rounded">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Candidat</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr>
                        <td class="px-6 py-4">
                            <input type="checkbox" class="reminder-checkbox rounded" value="1">
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-gray-900">Jean Dupont</div>
                            <div class="text-sm text-gray-500"><EMAIL></div>
                        </td>
                        <td class="px-6 py-4">
                            <button class="send-individual-email text-blue-600 hover:text-blue-900"
                                    data-reminder-id="1"
                                    data-candidate-name="Jean Dupont"
                                    data-candidate-email="<EMAIL>">
                                Envoyer email
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4">
                            <input type="checkbox" class="reminder-checkbox rounded" value="2">
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-gray-900">Marie Martin</div>
                            <div class="text-sm text-gray-500"><EMAIL></div>
                        </td>
                        <td class="px-6 py-4">
                            <button class="send-individual-email text-blue-600 hover:text-blue-900"
                                    data-reminder-id="2"
                                    data-candidate-name="Marie Martin"
                                    data-candidate-email="<EMAIL>">
                                Envoyer email
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4">
                            <input type="checkbox" class="reminder-checkbox rounded" value="3">
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-gray-900">Pierre Durand</div>
                            <div class="text-sm text-gray-500"><EMAIL></div>
                        </td>
                        <td class="px-6 py-4">
                            <button class="send-individual-email text-blue-600 hover:text-blue-900"
                                    data-reminder-id="3"
                                    data-candidate-name="Pierre Durand"
                                    data-candidate-email="<EMAIL>">
                                Envoyer email
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Console de debug -->
        <div class="mt-6 bg-black text-green-400 p-4 rounded-lg">
            <h3 class="text-white font-semibold mb-2">Console de debug</h3>
            <div id="debug-console" class="font-mono text-sm h-64 overflow-y-auto"></div>
        </div>
        
        <!-- Modals de test -->
        <div id="individual-email-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
            <div class="relative top-20 mx-auto p-5 border w-1/2 shadow-lg rounded-md bg-white">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Modal Email Individuel</h3>
                <p id="modal-content">Contenu du modal</p>
                <button id="close-individual-modal" class="mt-4 px-4 py-2 bg-gray-500 text-white rounded">Fermer</button>
            </div>
        </div>
        
        <div id="bulk-email-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
            <div class="relative top-20 mx-auto p-5 border w-1/2 shadow-lg rounded-md bg-white">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Modal Email Groupé</h3>
                <p id="bulk-modal-content">Contenu du modal groupé</p>
                <button id="close-bulk-modal" class="mt-4 px-4 py-2 bg-gray-500 text-white rounded">Fermer</button>
            </div>
        </div>
    </div>

    <script>
        // COPIE EXACTE DU JAVASCRIPT SIMPLIFIÉ
        console.log('🚀 SCRIPT CHARGÉ');
        
        const debugConsole = document.getElementById('debug-console');
        const statusMessages = document.getElementById('status-messages');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-red-400' : type === 'success' ? 'text-green-400' : 'text-blue-400';
            debugConsole.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            debugConsole.scrollTop = debugConsole.scrollHeight;
            console.log(message);
        }
        
        function addStatus(message, type = 'info') {
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            const color = type === 'success' ? 'text-green-600' : type === 'error' ? 'text-red-600' : type === 'warning' ? 'text-yellow-600' : 'text-blue-600';
            statusMessages.innerHTML += `<div class="${color}">${icon} ${message}</div>`;
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🔥 DOM PRÊT');
            addStatus('DOM chargé', 'success');
            
            // Test de base
            const checkboxes = document.querySelectorAll('.reminder-checkbox');
            const counter = document.getElementById('selected-count');
            const bulkBtn = document.getElementById('bulk-email-btn');
            
            addLog(`ÉLÉMENTS: ${checkboxes.length} ${!!counter} ${!!bulkBtn}`);
            addStatus(`Éléments trouvés: ${checkboxes.length} checkboxes`, checkboxes.length > 0 ? 'success' : 'error');
            
            if (checkboxes.length === 0) {
                addLog('ERREUR: Aucune checkbox trouvée !', 'error');
                addStatus('ERREUR: Aucune checkbox trouvée !', 'error');
                alert('ERREUR: Aucune checkbox trouvée !');
                return;
            }
            
            // Fonction simple de mise à jour
            function updateCount() {
                const checked = document.querySelectorAll('.reminder-checkbox:checked').length;
                if (counter) counter.textContent = `${checked} candidat(s) sélectionné(s)`;
                if (bulkBtn) bulkBtn.disabled = checked === 0;
                addLog(`Compteur: ${checked}`);
            }
            
            // Ajouter les événements aux checkboxes
            checkboxes.forEach(function(checkbox) {
                checkbox.addEventListener('change', function() {
                    addLog(`Checkbox changée: ${this.checked}`, 'success');
                    updateCount();
                });
            });
            
            // Bouton "Tout sélectionner"
            const selectAllBtn = document.getElementById('select-all-btn');
            if (selectAllBtn) {
                selectAllBtn.addEventListener('click', function() {
                    addLog('Tout sélectionner cliqué', 'success');
                    const allChecked = document.querySelectorAll('.reminder-checkbox:checked').length === checkboxes.length;
                    checkboxes.forEach(function(cb) {
                        cb.checked = !allChecked;
                    });
                    updateCount();
                    this.textContent = allChecked ? 'Tout sélectionner' : 'Tout désélectionner';
                });
            }
            
            // Boutons email individuel
            const emailBtns = document.querySelectorAll('.send-individual-email');
            emailBtns.forEach(function(btn) {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    addLog('Email individuel cliqué', 'success');
                    const modal = document.getElementById('individual-email-modal');
                    const content = document.getElementById('modal-content');
                    if (modal && content) {
                        content.textContent = `Email pour: ${this.dataset.candidateName}`;
                        modal.classList.remove('hidden');
                        addLog('Modal ouvert', 'success');
                    } else {
                        addLog('Modal non trouvé !', 'error');
                        alert('Modal non trouvé !');
                    }
                });
            });
            
            // Bouton email groupé
            if (bulkBtn) {
                bulkBtn.addEventListener('click', function() {
                    addLog('Email groupé cliqué', 'success');
                    const modal = document.getElementById('bulk-email-modal');
                    const content = document.getElementById('bulk-modal-content');
                    if (modal && content) {
                        const checked = document.querySelectorAll('.reminder-checkbox:checked').length;
                        content.textContent = `Email groupé pour ${checked} candidats`;
                        modal.classList.remove('hidden');
                        addLog('Modal groupé ouvert', 'success');
                    } else {
                        addLog('Modal groupé non trouvé !', 'error');
                        alert('Modal groupé non trouvé !');
                    }
                });
            }
            
            // Fermer modals
            document.getElementById('close-individual-modal')?.addEventListener('click', function() {
                document.getElementById('individual-email-modal').classList.add('hidden');
            });
            
            document.getElementById('close-bulk-modal')?.addEventListener('click', function() {
                document.getElementById('bulk-email-modal').classList.add('hidden');
            });
            
            // Initialisation
            updateCount();
            addLog('✅ SYSTÈME INITIALISÉ', 'success');
            addStatus('Système initialisé avec succès', 'success');
            addStatus('Vous pouvez maintenant tester l\'interface', 'info');
        });
    </script>
</body>
</html>
